import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { completeSellerOnboarding, reset } from '../../redux/slices/userSlice';
import { getCurrentUser, updateUserOnboardingStatus, uploadProfileImage } from '../../redux/slices/authSlice';
import { showSuccess, showError } from '../../utils/toast';
import SellerOnboardingStep1 from './SellerOnboardingStep1';
import './SellerOnboarding.css';

const SellerOnboarding = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isLoading, isSuccess, isError, error, onboardingData } = useSelector((state) => state.user);

  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    description: '',
    profilePic: '',
    selectedImageFile: null, // Store selected file for deferred upload
    experiences: [{ schoolName: '', position: '', fromYear: '', toYear: '' }],
    minTrainingCost: '',
    socialLinks: {
      facebook: '',
      linkedin: '',
      twitter: '',
    },
    sports: [], // Will be populated from user selection
    expertise: [], // Will be populated from user selection
    certifications: [], // Optional field
  });

  // State for inline error messages
  const [fieldErrors, setFieldErrors] = useState({
    description: '',
    experiences: '',
    minTrainingCost: ''
  });

  // State for submission process
  const [submissionState, setSubmissionState] = useState({
    isUploadingImage: false,
    isSubmittingForm: false,
    uploadProgress: ''
  });

  // Disable page interactions during onboarding
  useEffect(() => {
    document.body.style.pointerEvents = 'none';
    const onboardingWrapper = document.querySelector('.seller-onboarding-wrapper');
    if (onboardingWrapper) {
      onboardingWrapper.style.pointerEvents = 'all';
    }

    return () => {
      document.body.style.pointerEvents = 'all';
    };
  }, []);

  // Handle successful completion
  useEffect(() => {
    if (isSuccess) {
      // Reset submission state
      setSubmissionState({
        isUploadingImage: false,
        isSubmittingForm: false,
        uploadProgress: ''
      });

      // Show success toast notification
      showSuccess('Onboarding completed successfully! Welcome to XO Sports Hub!', {
        autoClose: 4000,
      });

      // Update the authSlice user data immediately with the completed onboarding data
      if (onboardingData) {
        dispatch(updateUserOnboardingStatus(onboardingData));
      }

      // Also refresh the user data from server to ensure consistency
      dispatch(getCurrentUser()).then(() => {
        dispatch(reset());
        // Navigate after a short delay to allow toast to be seen
        setTimeout(() => {
          navigate('/seller/dashboard');
        }, 1500);
      });
    }
  }, [isSuccess, dispatch, navigate, onboardingData]);

  const handleInputChange = (field, value) => {
    console.log(`Updating ${field}:`, value);

    setFormData(prev => {
      const updated = {
        ...prev,
        [field]: value
      };
      return updated;
    });

    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Validate Step 1 fields before proceeding to Step 2
  const validateStep1 = () => {
    const errors = {};
    let hasErrors = false;

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
      hasErrors = true;
    }

    if (formData.experiences.length === 0 || !formData.experiences[0].schoolName) {
      errors.experiences = 'At least one experience with school name is required';
      hasErrors = true;
    }

    if (hasErrors) {
      setFieldErrors(errors);
      return false;
    }

    // Clear any existing errors
    setFieldErrors({
      description: '',
      experiences: '',
      minTrainingCost: ''
    });

    return true;
  };

  // Handle next button click with validation
  const handleNext = () => {
    if (validateStep1()) {
      setStep(2);
    }
  };

  // Monitor profilePic changes
  useEffect(() => {
    console.log('Current formData.profilePic:', formData.profilePic);
  }, [formData.profilePic]);

  const handleSocialChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      socialLinks: {
        ...prev.socialLinks,
        [field]: value
      }
    }));
  };

  const handleExperienceChange = (index, field, value) => {
    const updatedExperiences = [...formData.experiences];
    updatedExperiences[index] = { ...updatedExperiences[index], [field]: value };
    setFormData(prev => ({ ...prev, experiences: updatedExperiences }));
  };

  const addExperience = () => {
    setFormData(prev => ({
      ...prev,
      experiences: [...prev.experiences, { schoolName: '', position: '', fromYear: '', toYear: '' }]
    }));
  };

  const handleSubmit = async () => {
    // Clear previous errors
    setFieldErrors({
      description: '',
      experiences: '',
      minTrainingCost: ''
    });

    // Validate Step 2 required fields (Step 1 is already validated)
    const errors = {};
    let hasErrors = false;

    // Only validate minimum training cost since other fields are validated in Step 1
    if (!formData.minTrainingCost) {
      errors.minTrainingCost = 'Minimum training cost is required';
      hasErrors = true;
    }

    // Double-check Step 1 fields in case user navigated back and modified them
    if (!formData.description.trim()) {
      errors.description = 'Description is required';
      hasErrors = true;
    }

    if (formData.experiences.length === 0 || !formData.experiences[0].schoolName) {
      errors.experiences = 'At least one experience with school name is required';
      hasErrors = true;
    }

    if (hasErrors) {
      setFieldErrors(errors);
      // If Step 1 fields are invalid, go back to Step 1
      if (errors.description || errors.experiences) {
        setStep(1);
      }
      return;
    }

    try {
      let profilePicUrl = formData.profilePic;

      // Step 1: Upload image if one was selected
      if (formData.selectedImageFile) {
        setSubmissionState(prev => ({
          ...prev,
          isUploadingImage: true,
          uploadProgress: 'Uploading profile image...'
        }));

        console.log('Uploading selected image:', formData.selectedImageFile.name);

        try {
          const uploadResult = await dispatch(uploadProfileImage(formData.selectedImageFile)).unwrap();
          profilePicUrl = uploadResult.data.fileUrl;
          console.log('Image uploaded successfully:', profilePicUrl);

          setSubmissionState(prev => ({
            ...prev,
            isUploadingImage: false,
            uploadProgress: 'Image uploaded successfully!'
          }));
        } catch (uploadError) {
          console.error('Image upload failed:', uploadError);
          setSubmissionState(prev => ({
            ...prev,
            isUploadingImage: false,
            uploadProgress: ''
          }));

          showError('Failed to upload profile image. Please try again.');
          return;
        }
      }

      // Step 2: Submit form data with uploaded image URL
      setSubmissionState(prev => ({
        ...prev,
        isSubmittingForm: true,
        uploadProgress: 'Submitting onboarding data...'
      }));

      // Transform data to match API format - provide default values for required backend fields
      const submitData = {
        description: formData.description,
        profilePic: profilePicUrl,
        experiences: formData.experiences.map(exp => ({
          schoolName: exp.schoolName,
          position: exp.position,
          fromYear: parseInt(exp.fromYear) || new Date().getFullYear(),
          toYear: parseInt(exp.toYear) || new Date().getFullYear()
        })),
        minTrainingCost: parseFloat(formData.minTrainingCost),
        socialLinks: formData.socialLinks,
        sports: formData.sports.length > 0 ? formData.sports : ['General Sports'], // Default if empty
        expertise: formData.expertise.length > 0 ? formData.expertise : ['General Training'], // Default if empty
        certifications: formData.certifications
      };

      console.log('Submitting onboarding data:', submitData);
      dispatch(completeSellerOnboarding(submitData));

    } catch (error) {
      console.error('Submission process failed:', error);
      setSubmissionState(prev => ({
        ...prev,
        isUploadingImage: false,
        isSubmittingForm: false,
        uploadProgress: ''
      }));

      showError('An error occurred during submission. Please try again.');
    }
  };

  return (
    <div className="seller-onboarding-wrapper max-container">
      {step === 1 ? (
        <SellerOnboardingStep1
          formData={formData}
          onInputChange={handleInputChange}
          onExperienceChange={handleExperienceChange}
          onAddExperience={addExperience}
          onNext={handleNext}
          fieldErrors={fieldErrors}
        />
      ) : (
        <div className="seller-onboarding-step2-container">
          {/* Stepper */}
          <div className="progress-bar">
            <div className="step complete">1</div>
            <div className="progress-line" />
            <div className="step active">2</div>
          </div>

          {/* Minimum Customer Training Cost */}
          <div className="section-block">
            <div className="section-title">Minimum Customer Training Cost</div>
            <input
              type="number"
              className={`input min-cost-input ${fieldErrors.minTrainingCost ? 'error' : ''}`}
              placeholder="Enter amount"
              value={formData.minTrainingCost}
              onChange={e => handleInputChange('minTrainingCost', e.target.value)}
            />
            {fieldErrors.minTrainingCost && (
              <div className="field-error">{fieldErrors.minTrainingCost}</div>
            )}
          </div>

          {/* Social Media Account */}
          <div className="section-block">
            <div className="section-title">Social Media Account</div>
            <div className="social-inputs-grid">
              <div className="social-input-row">
                <span className="social-icon facebook">
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><path d="M18 10A8 8 0 1 0 10 18V12.89H8.1V10.89H10V9.22C10 7.5 11.17 6.5 12.72 6.5C13.44 6.5 14.2 6.62 14.2 6.62V8.6H13.23C12.27 8.6 12 9.18 12 9.77V10.89H14.1L13.8 12.89H12V18A8 8 0 0 0 18 10Z" fill="#1877F3" /><path d="M13.8 12.89L14.1 10.89H12V9.77C12 9.18 12.27 8.6 13.23 8.6H14.2V6.62S13.44 6.5 12.72 6.5C11.17 6.5 10 7.5 10 9.22V10.89H8.1V12.89H10V18C10.67 18 11.32 17.93 11.95 17.8V12.89H13.8Z" fill="#fff" /></svg>
                </span>
                <input
                  type="text"
                  className="input social-input"
                  placeholder="Facebook URL"
                  value={formData.socialLinks.facebook}
                  onChange={e => handleSocialChange('facebook', e.target.value)}
                />
              </div>
              <div className="social-input-row">
                <span className="social-icon linkedin">
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" fill="#0A66C2" /><path d="M6.94 8.5H4.98V15H6.94V8.5ZM5.96 7.5C6.6 7.5 7.1 7 7.1 6.36C7.1 5.72 6.6 5.22 5.96 5.22C5.32 5.22 4.82 5.72 4.82 6.36C4.82 7 5.32 7.5 5.96 7.5ZM15 15H13.04V11.5C13.04 10.67 12.37 10 11.54 10C10.71 10 10.04 10.67 10.04 11.5V15H8.08V8.5H10.04V9.38C10.41 8.81 11.13 8.5 11.54 8.5C13.01 8.5 15 9.44 15 11.5V15Z" fill="#fff" /></svg>
                </span>
                <input
                  type="text"
                  className="input social-input"
                  placeholder="LinkedIn URL"
                  value={formData.socialLinks.linkedin}
                  onChange={e => handleSocialChange('linkedin', e.target.value)}
                />
              </div>
              <div className="social-input-row">
                <span className="social-icon twitter">
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" fill="#1DA1F2" /><path d="M15.32 8.13C15.33 8.23 15.33 8.33 15.33 8.43C15.33 11.13 13.29 14.13 9.5 14.13C8.37 14.13 7.31 13.8 6.4 13.23C6.56 13.25 6.72 13.26 6.89 13.26C7.82 13.26 8.66 12.95 9.36 12.44C8.48 12.43 7.74 11.87 7.49 11.07C7.62 11.09 7.75 11.11 7.89 11.11C8.08 11.11 8.27 11.08 8.45 11.03C7.54 10.85 6.85 10.03 6.85 9.06V9.04C7.11 9.19 7.42 9.28 7.75 9.29C7.19 8.91 6.81 8.28 6.81 7.57C6.81 7.23 6.9 6.92 7.07 6.66C8.04 7.84 9.47 8.62 11.07 8.7C11.04 8.56 11.03 8.41 11.03 8.27C11.03 7.29 11.82 6.5 12.8 6.5C13.29 6.5 13.73 6.7 14.03 7.04C14.4 6.97 14.75 6.85 15.06 6.68C14.95 7.06 14.7 7.37 14.37 7.56C14.7 7.53 15.01 7.44 15.32 7.3C15.06 7.62 14.72 7.89 14.34 8.13H15.32Z" fill="#fff" /></svg>
                </span>
                <input
                  type="text"
                  className="input social-input"
                  placeholder="Twitter URL"
                  value={formData.socialLinks.twitter}
                  onChange={e => handleSocialChange('twitter', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Progress Display */}
          {(submissionState.isUploadingImage || submissionState.isSubmittingForm) && (
            <div className="submission-progress">
              <div className="progress-message">{submissionState.uploadProgress}</div>
              <div className="progress-steps">
                <div className={`progress-step ${submissionState.isUploadingImage ? 'active' : submissionState.isSubmittingForm ? 'completed' : ''}`}>
                  {submissionState.isUploadingImage ? '⏳' : submissionState.isSubmittingForm ? '✅' : '⭕'} Upload Image
                </div>
                <div className={`progress-step ${submissionState.isSubmittingForm ? 'active' : ''}`}>
                  {submissionState.isSubmittingForm ? '⏳' : '⭕'} Submit Form
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="next-btn-row">
            <button
              className="btn btn-outline"
              onClick={() => setStep(1)}
              disabled={submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
            >
              Back
            </button>
            <button
              className="btn btn-primary next-btn"
              onClick={handleSubmit}
              disabled={submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
            >
              {submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading
                ? (submissionState.uploadProgress || 'Processing...')
                : 'Complete Onboarding'}
            </button>
          </div>

          {/* Error Display */}
          {isError && (
            <div className="error-message">
              {error?.message || 'An error occurred. Please try again.'}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SellerOnboarding;