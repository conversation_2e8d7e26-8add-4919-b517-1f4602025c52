/* ContentDetailModal Component Styles */
.ContentDetailModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500; /* Above navbar (1000) and sidebar (200) */
  display: flex;
  align-items: center;
  justify-content: center;
}

.ContentDetailModal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.ContentDetailModal__container {
  position: relative;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.ContentDetailModal__header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  padding: var(--heading6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  flex: 1;
}

.content-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: var(--border-radius);
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--heading5);
  overflow: hidden;
  flex-shrink: 0;
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content-basic-info h2 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading5);
  font-weight: 600;
}

.content-badges {
  display: flex;
  gap: var(--smallfont);
  flex-wrap: wrap;
}

.status-badge,
.category-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius-small);
  font-size: var(--smallfont);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.status-pending {
  background-color: var(--warning-color);
  color: var(--white);
}

.status-badge.status-approved {
  background-color: var(--success-color);
  color: var(--white);
}

.status-badge.status-rejected {
  background-color: var(--error-color);
  color: var(--white);
}

.status-badge.status-draft {
  background-color: var(--gray);
  color: var(--white);
}

.category-badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--white);
}

.close-btn {
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--heading5);
  cursor: pointer;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Content */
.ContentDetailModal__content {
  flex: 1;
  padding: var(--heading6);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Sections */
.info-section,
.actions-section {
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
}

.section-header h3 {
  margin: 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: var(--smallfont);
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  background-color: var(--bg-gray);
}

.info-icon {
  color: var(--primary-color);
  font-size: var(--basefont);
  margin-top: 2px;
  flex-shrink: 0;
}

.info-item > div {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.info-label {
  font-size: var(--smallfont);
  color: var(--gray);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 400;
  word-break: break-word;
}

/* Edit Form */
.edit-form {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.form-group label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.form-input,
.form-textarea,
.form-select {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: var(--basefont);
  justify-content: flex-end;
  margin-top: var(--basefont);
}

/* Action Buttons */
.actions-section {
  background-color: var(--bg-gray);
}

.action-buttons {
  display: flex;
  gap: var(--basefont);
  flex-wrap: wrap;
}

.btn {
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.btn-success {
  background-color: var(--success-color);
  color: var(--white);
}

.btn-success:hover:not(:disabled) {
  background-color: var(--success-hover);
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--white);
}

.btn-warning:hover:not(:disabled) {
  background-color: var(--warning-hover);
}

.btn-danger {
  background-color: var(--error-color);
  color: var(--white);
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--error-hover);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ContentDetailModal__container {
    width: 95%;
    max-height: 95vh;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column;
  }
}
