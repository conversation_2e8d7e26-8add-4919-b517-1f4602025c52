/* SellerCards Component Styles */
.SellerCards {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  min-height: 500px;
}

/* Header Section */
.SellerCards__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--light-gray);
}

.SellerCards__subtitle {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.SellerCards__add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: 12px 20px;
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.SellerCards__add-btn:hover {
  background-color: var(--btn-hover-color);
}

/* Cards List View */
.SellerCards__cards-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.SellerCards__card-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: box-shadow 0.2s ease;
}

.SellerCards__card-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.SellerCards__card-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.SellerCards__card-logo {
  width: 50px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.SellerCards__card-logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.SellerCards__card-number {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  letter-spacing: 1px;
}

.SellerCards__delete-btn {
  background: none;
  border: none;
  color: var(--error-color);
  font-size: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

.SellerCards__delete-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

/* Empty State */
.SellerCards__empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--dark-gray);
}

.SellerCards__empty-state p {
  font-size: var(--basefont);
  margin: 0;
}

/* Add Card Form */
.SellerCards__form {
  max-width: 500px;
  margin: 0 auto;
}

.SellerCards__form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.SellerCards__input-field {
  flex: 1;
}

.SellerCards__input-field--full {
  flex: 1 1 100%;
}

.SellerCards__input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.SellerCards__input-icon {
  position: absolute;
  left: 12px;
  color: var(--dark-gray);
  font-size: 16px;
  z-index: 1;
}

.SellerCards__input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: "Poppins", sans-serif;
  transition: border-color 0.2s ease;
  background-color: var(--white);
}

.SellerCards__input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
}

.SellerCards__input::placeholder {
  color: var(--dark-gray);
}

/* Form Actions */
.SellerCards__form-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
}

.SellerCards__submit-btn {
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: 12px 32px;
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.SellerCards__submit-btn:hover {
  background-color: var(--btn-hover-color);
}

.SellerCards__cancel-btn {
  background-color: transparent;
  color: var(--dark-gray);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: 12px 32px;
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.SellerCards__cancel-btn:hover {
  background-color: var(--bg-gray);
  border-color: var(--dark-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
  .SellerCards {
    padding: 16px;
  }

  .SellerCards__header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .SellerCards__add-btn {
    justify-content: center;
  }

  .SellerCards__card-item {
    padding: 16px;
  }

  .SellerCards__card-content {
    gap: 12px;
  }

  .SellerCards__card-logo {
    width: 40px;
    height: 26px;
  }

  .SellerCards__form-row {
    flex-direction: column;
    gap: 12px;
  }

  .SellerCards__form-actions {
    flex-direction: column;
  }

  .SellerCards__submit-btn,
  .SellerCards__cancel-btn {
    width: 100%;
  }
}
