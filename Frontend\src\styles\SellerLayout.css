/* SellerLayout Component Styles */
.SellerLayout {
  display: flex;
  min-height: calc(100vh - 90px);
  padding: var(--heading6) 0;
  background-color: var(--bg-gray);
}

.SellerLayout .container {
  display: grid;
  grid-template-columns: 25% 1fr;
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--basefont);
  gap: var(--heading6);
}

.SellerLayout .sidebar {
  flex: 0 0 250px;
  position: sticky;
  top: 90px;
  height: calc(100vh - 120px);
}
.SellerLayout .outerdiv{
  width: 100%;

  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  padding:var(--heading6);
  overflow-x: scroll;
}
.SellerLayout .outerdiv::-webkit-scrollbar{
 display: none;
}
.SellerLayout .content {
  flex: 1;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-dark);
  padding: var(--heading6);
  display: flex;
  flex-direction: column;

}

/* SectionWrapper-style heading for SellerLayout */
.SellerLayout .bordrdiv {
  border-bottom: 1px solid #fddcdc;
  display: flex;
  justify-content: space-between;
}

.SellerLayout .bordrdiv h2 {
  display: inline-flex;
  align-items: center;
  background-color: #fddcdc;
  color: #0a0033;
  padding: 8px 16px;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  position: relative;
  font-weight: 600;
  font-family: sans-serif;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
  font-size: var(--heading5);
  margin: 0;
  gap: 8px;
}

.SellerLayout__title {
  display: inline-flex;
  align-items: center;
  background-color: #fddcdc;
  color: #0a0033;
  padding: 8px 16px;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  position: relative;
  font-weight: 600;
  font-family: sans-serif;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
  font-size: var(--heading5);
  margin: 0;
  gap: 8px;
}

.SellerLayout__subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0;
}

.SellerLayout__content {
  display: flex;
  flex-direction: column;
}



/* Utility class for margin-bottom */
.mb-30 {
  margin-bottom: 30px;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .SellerLayout .container {
    max-width: 100%;
    padding: 0 var(--smallfont);
  }

  .SellerLayout .sidebar {
    flex: 0 0 220px;
  }
}

@media (max-width: 768px) {
  .SellerLayout {
    padding: var(--smallfont) 0;
  }

  .SellerLayout .container {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
    padding: 0 var(--smallfont);
  }

  .SellerLayout .sidebar {
    position: relative;
    top: auto;
    height: auto;
    flex: none;
    display: none;
  }

  .SellerLayout .content {
    padding: var(--basefont);
    width: 100%;
    overflow-x: scroll;
  }

  .SellerLayout__title,
  .SellerLayout .bordrdiv h2 {
    font-size: var(--heading6);
    padding: 6px 12px;
  }

  .AddStrategy__header-container {
    gap: var(--basefont);
  }

  .AddStrategy__back-btn {
    font-size: var(--heading6);
    padding: 6px 12px;
  }

  .SellerLayout__subtitle {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .SellerLayout .content {
    padding: var(--smallfont);
  }

  .SellerLayout__title,
  .SellerLayout .bordrdiv h2 {
    font-size: var(--basefont);
    padding: 4px 8px;
  }



  .AddStrategy__back-btn {
    font-size: var(--basefont);
    padding: 4px 8px;
  }

  .mb-30 {
    margin-bottom: var(--basefont);
  }
}
.add-strategy-btn{
  margin-bottom: 5px;
}

/* AddStrategy Header Styling */
.AddStrategy__header-container {
  display: flex;
  align-items: center;
  gap: var(--heading5);
}

.AddStrategy__back-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: #fddcdc;
  color: #0a0033;
  padding: 8px 16px;
  border: none;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  font-weight: 600;
  font-family: sans-serif;
  font-size: var(--heading5);
  cursor: pointer;
  transition: opacity 0.3s ease;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
}

.AddStrategy__back-btn:hover {
  opacity: 0.8;
}

.AddStrategy__back-icon {
  font-size: var(--heading5);
}