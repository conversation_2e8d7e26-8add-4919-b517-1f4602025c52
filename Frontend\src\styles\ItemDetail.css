/* ItemDetail Component Styles */
.ItemDetail {
  padding: var(--heading5) 0;
  background-color: var(--white);
}

.ItemDetail__container {
  padding: 0 var(--basefont);
}

.ItemDetail__content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--heading4);
  margin-bottom: var(--heading3);
}

/* Main Content Styles */
.ItemDetail__mainContent {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
}

.ItemDetail__title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  line-height: 1.3;
  margin: 0;
}

.ItemDetail__coach {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0;
}

.ItemDetail__imageContainer {
  width: 100%;
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.ItemDetail__image {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  display: block;
}

/* Tabs Styles */
.ItemDetail__tabs {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.ItemDetail__tabButtons {
  display: flex;
  gap: 0;
  border-bottom: 1px solid var(--light-gray);
}

.ItemDetail__tabButton {
  background: none;
  border: none;
  padding: var(--basefont) var(--heading5);
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--dark-gray);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.ItemDetail__tabButton:hover {
  color: var(--btn-color);
}

.ItemDetail__tabButton--active {
  color: var(--btn-color);
  border-bottom-color: var(--btn-color);
}

.ItemDetail__tabContent {
  padding: var(--heading5) 0;
}

.ItemDetail__tabPanel {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.ItemDetail__description {
  font-size: var(--basefont);
  color: var(--text-color);
  line-height: 1.6;
  margin: 0;
}

.ItemDetail__seeMore {
  color: var(--btn-color);
  font-size: var(--basefont);
  font-weight: 500;
  text-decoration: none;
  align-self: flex-start;
  transition: color 0.3s ease;
}

.ItemDetail__seeMore:hover {
  color: var(--primary-color);
}

/* Sidebar Styles */
.ItemDetail__sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
  height: fit-content;
  position: sticky;
  top: var(--heading5);
}

.ItemDetail__priceBox {
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
  box-shadow: var(--box-shadow-light);
}

.ItemDetail__price {
  font-size: var(--heading3);
  font-weight: 700;
  color: var(--secondary-color);
  text-align: center;
  margin-bottom: var(--basefont);
}

.ItemDetail__buyButton {
  width: 100%;
  margin-bottom: var(--smallfont);
}

.ItemDetail__addToCartButton {
  width: 100%;
}

.ItemDetail__contentIncludes,
.ItemDetail__contentInfo {
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
  box-shadow: var(--box-shadow-light);
}

.ItemDetail__sidebarTitle {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 var(--basefont) 0;
}

.ItemDetail__includesList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.ItemDetail__includesItem {
  font-size: var(--smallfont);
  color: var(--text-color);
  position: relative;
  padding-left: var(--heading5);
}

.ItemDetail__includesItem::before {
  content: "•";
  color: var(--btn-color);
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0;
}

.ItemDetail__infoList {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.ItemDetail__infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) 0;
  border-bottom: 1px solid var(--bg-gray);
}

.ItemDetail__infoItem:last-child {
  border-bottom: none;
}

.ItemDetail__infoLabel {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.ItemDetail__infoValue {
  font-size: var(--smallfont);
  color: var(--text-color);
  font-weight: 600;
}

/* Related Section Styles */
.ItemDetail__relatedSection {
  padding: var(--heading3) 0;
  background-color: var(--bg-gray);
}

.ItemDetail__relatedTitle {
  font-size: var(--heading3);
  font-weight: 600;
  color: var(--secondary-color);
  text-align: center;
  margin: 0 0 var(--basefont) 0;
}

.ItemDetail__learnMoreLink {
  display: block;
  text-align: center;
  color: var(--btn-color);
  font-size: var(--basefont);
  font-weight: 500;
  text-decoration: none;
  margin-bottom: var(--heading4);
  transition: color 0.3s ease;
}

.ItemDetail__learnMoreLink:hover {
  color: var(--primary-color);
}

.ItemDetail__relatedGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--heading5);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .ItemDetail__content {
    grid-template-columns: 1fr;
    gap: var(--heading5);
  }
  
  .ItemDetail__sidebar {
    position: static;
    order: -1;
  }
  
  .ItemDetail__relatedGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .ItemDetail__title {
    font-size: var(--heading5);
  }
  
  .ItemDetail__tabButtons {
    flex-wrap: wrap;
  }
  
  .ItemDetail__tabButton {
    padding: var(--smallfont) var(--basefont);
    font-size: var(--smallfont);
  }
  
  .ItemDetail__relatedGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .ItemDetail__price {
    font-size: var(--heading4);
  }
}

@media (max-width: 480px) {
  .ItemDetail__container {
    padding: 0 var(--smallfont);
  }
  
  .ItemDetail__content {
    gap: var(--basefont);
  }
  
  .ItemDetail__mainContent {
    gap: var(--basefont);
  }
  
  .ItemDetail__sidebar {
    gap: var(--basefont);
  }
  
  .ItemDetail__priceBox,
  .ItemDetail__contentIncludes,
  .ItemDetail__contentInfo {
    padding: var(--basefont);
  }
  
  .ItemDetail__relatedGrid {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }
  
  .ItemDetail__tabButtons {
    justify-content: center;
  }
  
  .ItemDetail__tabButton {
    flex: 1;
    text-align: center;
    min-width: 0;
  }
}
