import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentCMSPage,
  selectLoading,
  hideCMSEditorModal,
  addCMSPage,
  updateCMSPage,
  setCMSLoading,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import "../../styles/CMSEditorModal.css";

// Icons
import { 
  FaTimes, FaSave, FaEye, FaCode, FaImage, FaLink, 
  FaBold, FaItalic, FaUnderline, FaListUl, FaListOl 
} from "react-icons/fa";
import { MdTitle, MdDescription } from "react-icons/md";

const CMSEditorModal = () => {
  const dispatch = useDispatch();
  const currentPage = useSelector(selectCurrentCMSPage);
  const loading = useSelector(selectLoading);
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
    metaDescription: '',
    status: 'Draft',
    featuredImage: '',
  });
  const [previewMode, setPreviewMode] = useState(false);
  const [errors, setErrors] = useState({});

  const isEditing = currentPage && currentPage.id;

  useEffect(() => {
    if (currentPage) {
      setFormData({
        title: currentPage.title || '',
        slug: currentPage.slug || '',
        content: currentPage.content || '',
        metaDescription: currentPage.metaDescription || '',
        status: currentPage.status || 'Draft',
        featuredImage: currentPage.featuredImage || '',
      });
    } else {
      // Reset form for new page
      setFormData({
        title: '',
        slug: '',
        content: '',
        metaDescription: '',
        status: 'Draft',
        featuredImage: '',
      });
    }
    setErrors({});
  }, [currentPage]);

  const handleClose = () => {
    dispatch(hideCMSEditorModal());
    setPreviewMode(false);
    setErrors({});
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-generate slug from title
    if (field === 'title' && !isEditing) {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({
        ...prev,
        slug: slug
      }));
    }

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Content is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    dispatch(setCMSLoading(true));
    
    // Simulate API call
    setTimeout(() => {
      const pageData = {
        ...formData,
        id: isEditing ? currentPage.id : Date.now(),
        lastModified: new Date().toISOString(),
        author: 'Admin',
      };

      if (isEditing) {
        dispatch(updateCMSPage(pageData));
        dispatch(addActivity({
          id: Date.now(),
          type: 'cms_update',
          description: `CMS page updated: ${formData.title}`,
          timestamp: new Date().toISOString(),
          user: 'Admin',
        }));
      } else {
        dispatch(addCMSPage(pageData));
        dispatch(addActivity({
          id: Date.now(),
          type: 'cms_create',
          description: `CMS page created: ${formData.title}`,
          timestamp: new Date().toISOString(),
          user: 'Admin',
        }));
      }

      dispatch(setCMSLoading(false));
      handleClose();
      
      // Show success message
      alert(`Page "${formData.title}" has been ${isEditing ? 'updated' : 'created'} successfully!`);
    }, 1000);
  };

  const insertTextAtCursor = (text) => {
    const textarea = document.getElementById('content-editor');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const content = formData.content;
    
    const newContent = content.substring(0, start) + text + content.substring(end);
    handleInputChange('content', newContent);
    
    // Set cursor position after inserted text
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + text.length, start + text.length);
    }, 0);
  };

  const formatText = (format) => {
    const textarea = document.getElementById('content-editor');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = formData.content.substring(start, end);
    
    if (!selectedText) {
      alert('Please select text to format');
      return;
    }

    let formattedText = '';
    switch (format) {
      case 'bold':
        formattedText = `**${selectedText}**`;
        break;
      case 'italic':
        formattedText = `*${selectedText}*`;
        break;
      case 'underline':
        formattedText = `<u>${selectedText}</u>`;
        break;
      default:
        formattedText = selectedText;
    }

    const content = formData.content;
    const newContent = content.substring(0, start) + formattedText + content.substring(end);
    handleInputChange('content', newContent);
  };

  return (
    <div className="CMSEditorModal">
      <div className="CMSEditorModal__overlay" onClick={handleClose} />
      <div className="CMSEditorModal__container">
        {/* Header */}
        <div className="CMSEditorModal__header">
          <div className="header-content">
            <h2>
              <MdTitle />
              {isEditing ? 'Edit Page' : 'Create New Page'}
            </h2>
            <div className="header-actions">
              <button 
                className={`btn ${previewMode ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => setPreviewMode(!previewMode)}
              >
                <FaEye />
                {previewMode ? 'Edit' : 'Preview'}
              </button>
            </div>
          </div>
          <button className="close-btn" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="CMSEditorModal__content">
          {!previewMode ? (
            <div className="editor-form">
              {/* Basic Information */}
              <div className="form-section">
                <h3>Basic Information</h3>
                <div className="form-row">
                  <div className="form-group">
                    <label>Page Title *</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      className={`form-input ${errors.title ? 'error' : ''}`}
                      placeholder="Enter page title"
                    />
                    {errors.title && <span className="error-message">{errors.title}</span>}
                  </div>
                  <div className="form-group">
                    <label>URL Slug *</label>
                    <input
                      type="text"
                      value={formData.slug}
                      onChange={(e) => handleInputChange('slug', e.target.value)}
                      className={`form-input ${errors.slug ? 'error' : ''}`}
                      placeholder="page-url-slug"
                    />
                    {errors.slug && <span className="error-message">{errors.slug}</span>}
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Status</label>
                    <select
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      className="form-select"
                    >
                      <option value="Draft">Draft</option>
                      <option value="Published">Published</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Featured Image URL</label>
                    <input
                      type="url"
                      value={formData.featuredImage}
                      onChange={(e) => handleInputChange('featuredImage', e.target.value)}
                      className="form-input"
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label>Meta Description</label>
                  <textarea
                    value={formData.metaDescription}
                    onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                    className="form-textarea"
                    rows="2"
                    placeholder="Brief description for search engines (160 characters max)"
                    maxLength="160"
                  />
                  <small className="char-count">{formData.metaDescription.length}/160</small>
                </div>
              </div>

              {/* Content Editor */}
              <div className="form-section">
                <h3>Page Content</h3>
                
                {/* Toolbar */}
                <div className="editor-toolbar">
                  <div className="toolbar-group">
                    <button type="button" onClick={() => formatText('bold')} title="Bold">
                      <FaBold />
                    </button>
                    <button type="button" onClick={() => formatText('italic')} title="Italic">
                      <FaItalic />
                    </button>
                    <button type="button" onClick={() => formatText('underline')} title="Underline">
                      <FaUnderline />
                    </button>
                  </div>
                  <div className="toolbar-group">
                    <button type="button" onClick={() => insertTextAtCursor('- ')} title="Bullet List">
                      <FaListUl />
                    </button>
                    <button type="button" onClick={() => insertTextAtCursor('1. ')} title="Numbered List">
                      <FaListOl />
                    </button>
                  </div>
                  <div className="toolbar-group">
                    <button type="button" onClick={() => insertTextAtCursor('[Link Text](URL)')} title="Insert Link">
                      <FaLink />
                    </button>
                    <button type="button" onClick={() => insertTextAtCursor('![Alt Text](Image URL)')} title="Insert Image">
                      <FaImage />
                    </button>
                  </div>
                </div>

                <div className="form-group">
                  <textarea
                    id="content-editor"
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    className={`form-textarea content-editor ${errors.content ? 'error' : ''}`}
                    rows="15"
                    placeholder="Write your page content here... You can use Markdown formatting."
                  />
                  {errors.content && <span className="error-message">{errors.content}</span>}
                </div>
              </div>
            </div>
          ) : (
            <div className="preview-content">
              <div className="preview-header">
                <h1>{formData.title || 'Untitled Page'}</h1>
                {formData.featuredImage && (
                  <img src={formData.featuredImage} alt={formData.title} className="featured-image" />
                )}
              </div>
              <div className="preview-body">
                <div dangerouslySetInnerHTML={{ 
                  __html: formData.content.replace(/\n/g, '<br>') 
                }} />
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="CMSEditorModal__footer">
          <div className="footer-info">
            {isEditing && (
              <span className="last-modified">
                Last modified: {new Date(currentPage.lastModified).toLocaleString()}
              </span>
            )}
          </div>
          <div className="footer-actions">
            <button 
              className="btn btn-outline"
              onClick={handleClose}
              disabled={loading.cms}
            >
              Cancel
            </button>
            <button 
              className="btn btn-primary"
              onClick={handleSave}
              disabled={loading.cms}
            >
              <FaSave />
              {loading.cms ? 'Saving...' : (isEditing ? 'Update Page' : 'Create Page')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CMSEditorModal;
