import React from "react";
import { useSelector } from "react-redux";
import { selectRequests } from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import { IoEyeSharp } from "react-icons/io5";
import { LiaComment } from "react-icons/lia";
import "../../styles/SellerRequests.css";

const SellerRequests = () => {
  const requests = useSelector(selectRequests);

  return (
    <SellerLayout>
      <div className="seller-requests-container">
        <table className="requests-table">
          <thead>
            <tr>
              <th>No.</th>
              <th>Request Id</th>
              <th>Videos/Documents</th>
              <th>Date</th>
              <th>Price</th>
              <th>Requested Amount</th>
              <th>Requested Customer</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {requests.map((item, index) => (
              <tr key={item.id}>
                <td>{index + 1}</td>
                <td>{item.id}</td>
                <td>
                  <div className="video-doc">
                    <img src={item.image} alt={item.title} />
                    <span>{item.title}</span>
                  </div>
                </td>
                <td>{item.date} | 4:50PM</td>
                <td>{item.price}</td>
                <td>{item.requestedAmount}</td>
                <td>{item.requestedCustomer}</td>
                <td>
                  <div className="action-icons">
                    <IoEyeSharp className="action-icon" />
                    <LiaComment className="action-icon" />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </SellerLayout>
  );
};

export default SellerRequests;
