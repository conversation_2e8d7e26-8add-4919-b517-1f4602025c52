/* ToastNotification Component Styles */
.ToastContainer {
  position: fixed;
  top: var(--heading6);
  right: var(--heading6);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
  pointer-events: none;
}

.ToastNotification {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 300px;
  max-width: 500px;
  padding: var(--basefont);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  background-color: var(--white);
  border-left: 4px solid;
  pointer-events: auto;
  animation: slideInRight 0.3s ease-out;
  transition: all 0.3s ease;
}

.ToastNotification:hover {
  transform: translateX(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Toast Types */
.toast-success {
  border-left-color: #10b981;
  background-color: #f0fdf4;
}

.toast-error {
  border-left-color: #ef4444;
  background-color: #fef2f2;
}

.toast-warning {
  border-left-color: #f59e0b;
  background-color: #fffbeb;
}

.toast-info {
  border-left-color: #3b82f6;
  background-color: #eff6ff;
}

/* Toast Content */
.toast-content {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  flex: 1;
}

.toast-icon {
  font-size: var(--heading6);
  flex-shrink: 0;
}

.toast-icon.success {
  color: #10b981;
}

.toast-icon.error {
  color: #ef4444;
}

.toast-icon.warning {
  color: #f59e0b;
}

.toast-icon.info {
  color: #3b82f6;
}

.toast-message {
  font-size: var(--smallfont);
  color: var(--text-color);
  font-weight: 500;
  line-height: 1.4;
}

/* Close Button */
.toast-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background-color: transparent;
  color: var(--dark-gray);
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-left: var(--smallfont);
}

.toast-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--secondary-color);
}

/* Position Variants */
.toast-top-right {
  animation: slideInRight 0.3s ease-out;
}

.toast-top-left {
  animation: slideInLeft 0.3s ease-out;
}

.toast-bottom-right {
  animation: slideInUpRight 0.3s ease-out;
}

.toast-bottom-left {
  animation: slideInUpLeft 0.3s ease-out;
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUpRight {
  from {
    transform: translateX(100%) translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0) translateY(0);
    opacity: 1;
  }
}

@keyframes slideInUpLeft {
  from {
    transform: translateX(-100%) translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0) translateY(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* Container Position Variants */
.ToastContainer.top-left {
  top: var(--heading6);
  left: var(--heading6);
  right: auto;
}

.ToastContainer.top-center {
  top: var(--heading6);
  left: 50%;
  right: auto;
  transform: translateX(-50%);
}

.ToastContainer.bottom-right {
  top: auto;
  bottom: var(--heading6);
  right: var(--heading6);
}

.ToastContainer.bottom-left {
  top: auto;
  bottom: var(--heading6);
  left: var(--heading6);
  right: auto;
}

.ToastContainer.bottom-center {
  top: auto;
  bottom: var(--heading6);
  left: 50%;
  right: auto;
  transform: translateX(-50%);
}

/* Responsive styles */
@media (max-width: 768px) {
  .ToastContainer {
    top: var(--basefont);
    right: var(--basefont);
    left: var(--basefont);
  }

  .ToastNotification {
    min-width: auto;
    max-width: none;
    width: 100%;
  }

  .toast-message {
    font-size: var(--extrasmallfont);
  }
}

@media (max-width: 480px) {
  .ToastContainer {
    top: var(--smallfont);
    right: var(--smallfont);
    left: var(--smallfont);
  }

  .ToastNotification {
    padding: var(--smallfont);
  }

  .toast-content {
    gap: 8px;
  }

  .toast-icon {
    font-size: var(--basefont);
  }

  .toast-close {
    width: 20px;
    height: 20px;
    margin-left: 8px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ToastNotification {
    background-color: #1f2937;
    color: #f9fafb;
  }

  .toast-success {
    background-color: #064e3b;
  }

  .toast-error {
    background-color: #7f1d1d;
  }

  .toast-warning {
    background-color: #78350f;
  }

  .toast-info {
    background-color: #1e3a8a;
  }

  .toast-message {
    color: #f9fafb;
  }

  .toast-close {
    color: #d1d5db;
  }

  .toast-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #f9fafb;
  }
}

/* Progress bar for timed toasts */
.ToastNotification::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: currentColor;
  opacity: 0.3;
  animation: progressBar 5s linear;
}

@keyframes progressBar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
