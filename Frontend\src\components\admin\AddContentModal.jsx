import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectLoading,
  addContent,
  setContentLoading,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import "../../styles/AddContentModal.css";

// Icons
import { 
  FaTimes, FaSave, FaUpload, FaVideo, FaImage, FaFile,
  FaDollarSign, FaUser, FaTag 
} from "react-icons/fa";
import { MdTitle, MdDescription, MdCategory } from "react-icons/md";

const AddContentModal = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const loading = useSelector(selectLoading);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'Football',
    price: '',
    seller: '',
    contentType: 'video',
    file: null,
    thumbnail: null,
    tags: '',
    status: 'pending',
  });
  const [errors, setErrors] = useState({});
  const [dragActive, setDragActive] = useState(false);

  if (!isOpen) return null;

  const handleClose = () => {
    setFormData({
      title: '',
      description: '',
      category: 'Football',
      price: '',
      seller: '',
      contentType: 'video',
      file: null,
      thumbnail: null,
      tags: '',
      status: 'pending',
    });
    setErrors({});
    onClose();
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleFileChange = (field, file) => {
    if (file) {
      // Validate file type
      const allowedTypes = field === 'file' 
        ? ['video/mp4', 'video/avi', 'video/mov', 'application/pdf', 'application/zip']
        : ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          [field]: `Invalid file type. Allowed: ${allowedTypes.join(', ')}`
        }));
        return;
      }

      // Validate file size (50MB for content, 5MB for thumbnail)
      const maxSize = field === 'file' ? 50 * 1024 * 1024 : 5 * 1024 * 1024;
      if (file.size > maxSize) {
        setErrors(prev => ({
          ...prev,
          [field]: `File too large. Maximum size: ${field === 'file' ? '50MB' : '5MB'}`
        }));
        return;
      }

      setFormData(prev => ({
        ...prev,
        [field]: file
      }));

      // Clear error
      if (errors[field]) {
        setErrors(prev => ({
          ...prev,
          [field]: ''
        }));
      }
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileChange('file', e.dataTransfer.files[0]);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = 'Valid price is required';
    }

    if (!formData.seller.trim()) {
      newErrors.seller = 'Seller name is required';
    }

    if (!formData.file) {
      newErrors.file = 'Content file is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    dispatch(setContentLoading(true));
    
    // Simulate API call
    setTimeout(() => {
      const contentData = {
        id: Date.now(),
        title: formData.title,
        description: formData.description,
        category: formData.category,
        price: parseFloat(formData.price),
        seller: formData.seller,
        contentType: formData.contentType,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        status: formData.status,
        uploadDate: new Date().toISOString(),
        thumbnail: formData.thumbnail ? URL.createObjectURL(formData.thumbnail) : null,
        fileUrl: formData.file ? URL.createObjectURL(formData.file) : null,
        views: 0,
        downloads: 0,
      };

      dispatch(addContent(contentData));
      dispatch(addActivity({
        id: Date.now(),
        type: 'content_upload',
        description: `New content added: ${formData.title}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));

      dispatch(setContentLoading(false));
      handleClose();
      
      // Show success message
      alert(`Content "${formData.title}" has been added successfully!`);
    }, 1500);
  };

  const categories = [
    'Football', 'Basketball', 'Baseball', 'Soccer', 'Tennis', 
    'Hockey', 'Golf', 'Swimming', 'Track & Field', 'Other'
  ];

  return (
    <div className="AddContentModal">
      <div className="AddContentModal__overlay" onClick={handleClose} />
      <div className="AddContentModal__container">
        {/* Header */}
        <div className="AddContentModal__header">
          <div className="header-content">
            <h2>
              <FaVideo />
              Add New Content
            </h2>
          </div>
          <button className="close-btn" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="AddContentModal__content">
          <div className="form-container">
            {/* Basic Information */}
            <div className="form-section">
              <h3>
                <MdTitle />
                Basic Information
              </h3>
              
              <div className="form-group">
                <label>Content Title *</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={`form-input ${errors.title ? 'error' : ''}`}
                  placeholder="Enter content title"
                />
                {errors.title && <span className="error-message">{errors.title}</span>}
              </div>

              <div className="form-group">
                <label>Description *</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className={`form-textarea ${errors.description ? 'error' : ''}`}
                  rows="4"
                  placeholder="Describe the content..."
                />
                {errors.description && <span className="error-message">{errors.description}</span>}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Category *</label>
                  <select
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="form-select"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Price ($) *</label>
                  <input
                    type="number"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', e.target.value)}
                    className={`form-input ${errors.price ? 'error' : ''}`}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                  />
                  {errors.price && <span className="error-message">{errors.price}</span>}
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Seller Name *</label>
                  <input
                    type="text"
                    value={formData.seller}
                    onChange={(e) => handleInputChange('seller', e.target.value)}
                    className={`form-input ${errors.seller ? 'error' : ''}`}
                    placeholder="Enter seller name"
                  />
                  {errors.seller && <span className="error-message">{errors.seller}</span>}
                </div>
                <div className="form-group">
                  <label>Content Type</label>
                  <select
                    value={formData.contentType}
                    onChange={(e) => handleInputChange('contentType', e.target.value)}
                    className="form-select"
                  >
                    <option value="video">Video</option>
                    <option value="document">Document</option>
                    <option value="archive">Archive</option>
                  </select>
                </div>
              </div>

              <div className="form-group">
                <label>Tags (comma-separated)</label>
                <input
                  type="text"
                  value={formData.tags}
                  onChange={(e) => handleInputChange('tags', e.target.value)}
                  className="form-input"
                  placeholder="strategy, training, advanced"
                />
              </div>
            </div>

            {/* File Upload */}
            <div className="form-section">
              <h3>
                <FaUpload />
                File Upload
              </h3>

              {/* Main Content File */}
              <div className="form-group">
                <label>Content File *</label>
                <div 
                  className={`file-upload-area ${dragActive ? 'drag-active' : ''} ${errors.file ? 'error' : ''}`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  {formData.file ? (
                    <div className="file-selected">
                      <FaFile />
                      <span>{formData.file.name}</span>
                      <button 
                        type="button" 
                        onClick={() => handleInputChange('file', null)}
                        className="remove-file"
                      >
                        <FaTimes />
                      </button>
                    </div>
                  ) : (
                    <div className="file-upload-placeholder">
                      <FaUpload />
                      <p>Drag and drop your file here, or click to browse</p>
                      <small>Supported: MP4, AVI, MOV, PDF, ZIP (Max: 50MB)</small>
                    </div>
                  )}
                  <input
                    type="file"
                    onChange={(e) => handleFileChange('file', e.target.files[0])}
                    className="file-input"
                    accept=".mp4,.avi,.mov,.pdf,.zip"
                  />
                </div>
                {errors.file && <span className="error-message">{errors.file}</span>}
              </div>

              {/* Thumbnail */}
              <div className="form-group">
                <label>Thumbnail Image (Optional)</label>
                <div className="thumbnail-upload">
                  {formData.thumbnail ? (
                    <div className="thumbnail-preview">
                      <img src={URL.createObjectURL(formData.thumbnail)} alt="Thumbnail" />
                      <button 
                        type="button" 
                        onClick={() => handleInputChange('thumbnail', null)}
                        className="remove-thumbnail"
                      >
                        <FaTimes />
                      </button>
                    </div>
                  ) : (
                    <div className="thumbnail-placeholder">
                      <FaImage />
                      <span>Upload Thumbnail</span>
                    </div>
                  )}
                  <input
                    type="file"
                    onChange={(e) => handleFileChange('thumbnail', e.target.files[0])}
                    className="file-input"
                    accept="image/*"
                  />
                </div>
                {errors.thumbnail && <span className="error-message">{errors.thumbnail}</span>}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="AddContentModal__footer">
          <div className="footer-actions">
            <button 
              className="btn btn-outline"
              onClick={handleClose}
              disabled={loading.content}
            >
              Cancel
            </button>
            <button 
              className="btn btn-primary"
              onClick={handleSubmit}
              disabled={loading.content}
            >
              <FaSave />
              {loading.content ? 'Adding Content...' : 'Add Content'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddContentModal;
