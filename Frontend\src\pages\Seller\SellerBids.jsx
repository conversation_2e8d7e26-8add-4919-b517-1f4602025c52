import React from "react";
import { useSelector } from "react-redux";
import { selectBids } from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import { BsThreeDotsVertical } from "react-icons/bs";
import "../../styles/SellerBids.css";

const SellerBids = () => {
  const bids = useSelector(selectBids);

  return (
    <SellerLayout>
      <div className="seller-bids-container">
        <table className="bids-table">
          <thead>
            <tr>
              <th>No.</th>
              <th>Bid Id</th>
              <th>Videos/Documents</th>
              <th>Date</th>
              <th>Price</th>
              <th>Bid Amount</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {bids.map((item, index) => (
              <tr key={item.id}>
                <td>{index + 1}</td>
                <td>{item.id}</td>
                <td>
                  <div className="video-doc">
                    <img src={item.image} alt={item.title} />
                    <span>{item.title}</span>
                  </div>
                </td>
                <td>{item.date} | 4:50PM</td>
                <td>{item.price}</td>
                <td>{item.bidAmount}</td>
                <td>
                  <BsThreeDotsVertical className="action-icon" />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </SellerLayout>
  );
};

export default SellerBids;
