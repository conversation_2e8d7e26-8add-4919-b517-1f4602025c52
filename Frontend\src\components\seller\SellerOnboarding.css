.seller-onboarding-wrapper {
  width: 100%;
  margin: 1rem auto;
  background: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;

}

.seller-onboarding-wrapper .seller-onboarding-step2-container {
  display: grid;
  justify-items: center;
  gap: 2rem;
  align-items: center;
  width: 100%;
}

.seller-onboarding-wrapper .progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  width: 50%;
}
.seller-onboarding-wrapper .step {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--light-gray);
  color: var(--dark-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--heading6);
  border: 2px solid var(--light-gray);
  transition: background 0.3s, color 0.3s;
}
.seller-onboarding-wrapper .step.active {
  background: var(--btn-color);
  color: var(--white);
  border: 2px solid var(--btn-color);
}
.seller-onboarding-wrapper .step.complete {
  background: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}
.seller-onboarding-wrapper .progress-line {
  flex: 1 1 60px;
  height: 2px;
  background: var(--light-gray);
  margin: 0 0.5rem;
}

.seller-onboarding-wrapper .section-block {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1.5rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  width: 100%;
}
.seller-onboarding-wrapper .section-title {
  font-size: var(--heading6);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
  position: absolute;
  top: -20px;
  left: 50%;
  width: max-content;
  background: var(--white);
  padding: 0 0.5rem;
  transform: translate(-50%, 10px);
}
.seller-onboarding-wrapper .min-cost-input {
  width: 100%;
  min-width: 0;
    padding: 0.5rem 0.75rem;
    font-size: var(--basefont);
    background: var(--white);
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius);
}

.seller-onboarding-wrapper .social-inputs-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.seller-onboarding-wrapper .social-input-row {
  display: flex;
  align-items: center;

  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);

}
.seller-onboarding-wrapper .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--white);
  box-shadow: var(--box-shadow-light);
}
.seller-onboarding-wrapper .social-icon.facebook svg {
  display: block;
}
.seller-onboarding-wrapper .social-icon.linkedin svg {
  display: block;
}
.seller-onboarding-wrapper .social-icon.twitter svg {
  display: block;
}
.seller-onboarding-wrapper .social-input {
  flex: 1 1 200px;
  min-width: 0;
  padding: 0.5rem 0.75rem;
  font-size: var(--basefont);
  background: var(--white);
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: 1px solid var(--light-gray);
}

.seller-onboarding-wrapper .next-btn-row {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}
.seller-onboarding-wrapper .next-btn {
  min-width: 160px;
  font-size: var(--basefont);
  border-radius: var(--border-radius-large);
}

.seller-onboarding-wrapper .error-message {
  background: var(--error-color);
  color: var(--white);
  padding: 1rem;
  border-radius: var(--border-radius);
  text-align: center;
  margin-top: 1rem;
  font-size: var(--basefont);
}

/* Inline field error styles */
.seller-onboarding-wrapper .field-error {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Error state for input fields */
.seller-onboarding-wrapper .input.error,
.seller-onboarding-wrapper .description-textarea.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
}

.seller-onboarding-wrapper .input.error:focus,
.seller-onboarding-wrapper .description-textarea.error:focus {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
  outline: none;
}

/* Submission progress styles */
.seller-onboarding-wrapper .submission-progress {
  background: var(--bg-blue);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1.5rem;
  margin: 1rem 0;
  text-align: center;
}

.seller-onboarding-wrapper .progress-message {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.seller-onboarding-wrapper .progress-steps {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.seller-onboarding-wrapper .progress-step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--smallfont);
  color: var(--dark-gray);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  background: var(--white);
  border: 1px solid var(--light-gray);
  transition: all 0.3s ease;
}

.seller-onboarding-wrapper .progress-step.active {
  background: var(--primary-light-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
  font-weight: 600;
}

.seller-onboarding-wrapper .progress-step.completed {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
  font-weight: 600;
}

@media (max-width: 900px) {
  .seller-onboarding-wrapper {
    padding: 1rem 0.5rem;
  }
}
@media (max-width: 600px) {
  .seller-onboarding-wrapper {
    padding: 0.5rem 0.8rem;
  }
  .seller-onboarding-wrapper .section-block {
    padding: 1rem 0.5rem;
  }
  .seller-onboarding-wrapper .next-btn {
    min-width: 120px;
    font-size: var(--smallfont);
  }
}