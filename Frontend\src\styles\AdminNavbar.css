/* AdminNavbar Component Styles */
.AdminNavbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: var(--white);
  border-bottom: 1px solid var(--light-gray);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000; /* Highest z-index to stay on top */
  height: 70px; /* Fixed height for consistency */
}

.AdminNavbar__container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--smallfont) var(--heading6);
  max-width: 100%;
  height: 100%; /* Fill the navbar height */
}

/* Left Section */
.AdminNavbar__left {
  display: flex;
  align-items: center;
  gap: var(--heading6);
  flex: 1;
}

.AdminNavbar__toggle {
  display: none;
  background: none;
  border: none;
  font-size: var(--heading6);
  color: var(--secondary-color);
  cursor: pointer;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.AdminNavbar__toggle:hover {
  background-color: var(--bg-gray);
  color: var(--btn-color);
}

/* Search Section */
.AdminNavbar__search {
  flex: 1;
  max-width: 500px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--heading6);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: var(--smallfont) var(--smallfont) var(--smallfont) 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  font-size: var(--basefont);
  background-color: var(--bg-gray);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--btn-color);
  background-color: var(--white);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.search-input::placeholder {
  color: var(--dark-gray);
}

/* Right Section */
.AdminNavbar__right {
  display: flex;
  align-items: center;
  gap: var(--heading6);
}

/* Notifications */
.AdminNavbar__notifications {
  position: relative;
}

.notification-btn {
  background: none;
  border: none;
  font-size: var(--heading5);
  color: var(--secondary-color);
  cursor: pointer;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  position: relative;
}

.notification-btn:hover {
  background-color: var(--bg-gray);
  color: var(--btn-color);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--btn-color);
  color: var(--white);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* Notifications Dropdown */
.notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1100; /* Above navbar */
  margin-top: var(--smallfont);
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.notifications-header h4 {
  margin: 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.notification-count {
  font-size: var(--smallfont);
  color: var(--btn-color);
  font-weight: 600;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--bg-gray);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.notification-item:hover {
  background-color: var(--bg-gray);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-content p {
  margin: 0 0 4px 0;
  font-size: var(--smallfont);
  color: var(--text-color);
}

.notification-time {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.notifications-footer {
  padding: var(--smallfont) var(--basefont);
  border-top: 1px solid var(--light-gray);
}

.view-all-btn {
  width: 100%;
  background: none;
  border: none;
  color: var(--btn-color);
  font-size: var(--smallfont);
  font-weight: 600;
  padding: var(--smallfont);
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: background-color 0.3s ease;
}

.view-all-btn:hover {
  background-color: var(--bg-gray);
}

/* Profile Section */
.AdminNavbar__profile {
  position: relative;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.profile-btn:hover {
  background-color: var(--bg-gray);
}

.profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--smallfont);
  overflow: hidden;
}

.profile-avatar.large {
  width: 48px;
  height: 48px;
  font-size: var(--heading6);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.profile-name {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--text-color);
}

.profile-role {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Profile Dropdown */
.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1100; /* Above navbar */
  margin-top: var(--smallfont);
}

.profile-dropdown-header {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.profile-details h4 {
  margin: 0 0 4px 0;
  font-size: var(--smallfont);
  color: var(--text-color);
}

.profile-details p {
  margin: 0;
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.profile-dropdown-menu {
  padding: var(--smallfont) 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  width: 100%;
  background: none;
  border: none;
  padding: var(--smallfont) var(--basefont);
  font-size: var(--smallfont);
  color: var(--text-color);
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-align: left;
}

.dropdown-item:hover {
  background-color: var(--bg-gray);
}

.dropdown-item.logout {
  color: var(--btn-color);
}

.dropdown-item.logout:hover {
  background-color: rgba(238, 52, 37, 0.1);
}

.dropdown-icon {
  font-size: var(--smallfont);
}

.dropdown-divider {
  border: none;
  border-top: 1px solid var(--light-gray);
  margin: var(--smallfont) 0;
}

/* Overlay */
.AdminNavbar__overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminNavbar {
    height: 65px; /* Slightly smaller on tablet */
  }
}

@media (max-width: 768px) {
  .AdminNavbar {
    height: 60px; /* Mobile height */
  }

  .AdminNavbar__container {
    padding: var(--smallfont);
  }

  .AdminNavbar__left {
    gap: var(--smallfont);
  }

  .AdminNavbar__search {
    max-width: 200px;
  }

  .search-input {
    font-size: var(--smallfont);
    padding: 8px 8px 8px 32px;
  }

  .profile-info {
    display: none;
  }

  .notifications-dropdown,
  .profile-dropdown {
    width: 280px;
    right: -20px;
  }
}

@media (max-width: 480px) {
  .AdminNavbar {
    height: 55px; /* Smaller mobile height */
  }

  .AdminNavbar__container {
    padding: 8px var(--smallfont);
  }

  .AdminNavbar__search {
    display: none;
  }

  .AdminNavbar__right {
    gap: var(--smallfont);
  }

  .notifications-dropdown,
  .profile-dropdown {
    width: 260px;
    right: -10px;
  }
}
