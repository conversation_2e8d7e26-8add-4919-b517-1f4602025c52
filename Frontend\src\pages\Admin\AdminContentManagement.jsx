import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectContent,
  selectSelectedContent,
  selectUI,
  setSelectedContent,
  showContentDetailModal,
  updateContent,
  deleteContent,
  approveContent,
  rejectContent,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import ContentDetailModal from "../../components/admin/ContentDetailModal";
import AddContentModal from "../../components/admin/AddContentModal";
import "../../styles/AdminContentManagement.css";

// Icons
import { FaVideo, FaSearch, FaFilter, FaEye, FaEdit, FaTrash, FaCheck, FaTimes } from "react-icons/fa";
import { MdAdd } from "react-icons/md";

const AdminContentManagement = () => {
  const dispatch = useDispatch();
  const content = useSelector(selectContent);
  const selectedContent = useSelector(selectSelectedContent);
  const ui = useSelector(selectUI);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showAddModal, setShowAddModal] = useState(false);

  // Filter content based on search and filters
  const filteredContent = content.filter(item => {
    const matchesSearch =
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.seller.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter;
    const matchesStatus = statusFilter === "all" || item.status === statusFilter;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(setSelectedContent(filteredContent.map(item => item.id)));
    } else {
      dispatch(setSelectedContent([]));
    }
  };

  // Handle individual select
  const handleSelectContent = (contentId) => {
    const newSelection = selectedContent.includes(contentId)
      ? selectedContent.filter(id => id !== contentId)
      : [...selectedContent, contentId];
    dispatch(setSelectedContent(newSelection));
  };

  // Handle content actions
  const handleContentAction = (contentItem, action) => {
    switch (action) {
      case 'view':
      case 'edit':
        dispatch(showContentDetailModal(contentItem));
        break;
      case 'approve':
        if (window.confirm(`Approve "${contentItem.title}"?`)) {
          dispatch(approveContent(contentItem.id));
          dispatch(addActivity({
            id: Date.now(),
            type: 'content_approval',
            description: `Content approved: ${contentItem.title}`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`Content "${contentItem.title}" has been approved!`);
        }
        break;
      case 'reject':
        const reason = prompt(`Reason for rejecting "${contentItem.title}":`);
        if (reason) {
          dispatch(rejectContent(contentItem.id));
          dispatch(addActivity({
            id: Date.now(),
            type: 'content_rejection',
            description: `Content rejected: ${contentItem.title} - Reason: ${reason}`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`Content "${contentItem.title}" has been rejected.`);
        }
        break;
      case 'delete':
        if (window.confirm(`Delete "${contentItem.title}"? This action cannot be undone.`)) {
          dispatch(deleteContent(contentItem.id));
          dispatch(addActivity({
            id: Date.now(),
            type: 'content_deletion',
            description: `Content deleted: ${contentItem.title}`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`Content "${contentItem.title}" has been deleted!`);
        }
        break;
      default:
        break;
    }
  };

  // Handle bulk actions
  const handleBulkAction = (action) => {
    if (selectedContent.length === 0) {
      alert('Please select content first');
      return;
    }

    switch (action) {
      case 'approve':
        if (window.confirm(`Approve ${selectedContent.length} selected items?`)) {
          selectedContent.forEach(id => {
            dispatch(approveContent(id));
          });
          dispatch(addActivity({
            id: Date.now(),
            type: 'bulk_content_approval',
            description: `Bulk approved ${selectedContent.length} content items`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`${selectedContent.length} items approved`);
          dispatch(setSelectedContent([]));
        }
        break;
      case 'reject':
        const reason = prompt(`Reason for rejecting ${selectedContent.length} items:`);
        if (reason) {
          selectedContent.forEach(id => {
            dispatch(rejectContent(id));
          });
          dispatch(addActivity({
            id: Date.now(),
            type: 'bulk_content_rejection',
            description: `Bulk rejected ${selectedContent.length} content items - Reason: ${reason}`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`${selectedContent.length} items rejected`);
          dispatch(setSelectedContent([]));
        }
        break;
      case 'delete':
        if (window.confirm(`Delete ${selectedContent.length} selected items? This action cannot be undone.`)) {
          selectedContent.forEach(id => {
            dispatch(deleteContent(id));
          });
          dispatch(addActivity({
            id: Date.now(),
            type: 'bulk_content_deletion',
            description: `Bulk deleted ${selectedContent.length} content items`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`${selectedContent.length} items deleted`);
          dispatch(setSelectedContent([]));
        }
        break;
      default:
        break;
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    switch (status) {
      case 'published':
        return 'status-badge published';
      case 'under_review':
        return 'status-badge under-review';
      case 'draft':
        return 'status-badge draft';
      case 'rejected':
        return 'status-badge rejected';
      default:
        return 'status-badge';
    }
  };

  // Get category badge class
  const getCategoryBadge = (category) => {
    switch (category) {
      case 'Training Videos':
        return 'category-badge training';
      case 'Courses':
        return 'category-badge courses';
      case 'E-books':
        return 'category-badge ebooks';
      case 'Live Sessions':
        return 'category-badge live';
      default:
        return 'category-badge';
    }
  };

  return (
    <AdminLayout>
      <div className="AdminContentManagement">
        {/* Header Actions */}
        <div className="AdminContentManagement__header">
          <div className="header-left">
            <div className="search-container">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search content by title or seller..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>

          <div className="header-right">
            <button
              className="btn btn-primary"
              onClick={() => setShowAddModal(true)}
            >
              <MdAdd />
              Add New Content
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="AdminContentManagement__filters">
          <div className="filter-group">
            <FaFilter className="filter-icon" />
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Categories</option>
              <option value="Training Videos">Training Videos</option>
              <option value="Courses">Courses</option>
              <option value="E-books">E-books</option>
              <option value="Live Sessions">Live Sessions</option>
            </select>
          </div>

          <div className="filter-group">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="under_review">Under Review</option>
              <option value="draft">Draft</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>

          {selectedContent.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">
                {selectedContent.length} selected
              </span>
              <button
                className="btn btn-success"
                onClick={() => handleBulkAction('approve')}
              >
                <FaCheck />
                Approve
              </button>
              <button
                className="btn btn-warning"
                onClick={() => handleBulkAction('reject')}
              >
                <FaTimes />
                Reject
              </button>
              <button
                className="btn btn-danger"
                onClick={() => handleBulkAction('delete')}
              >
                Delete
              </button>
            </div>
          )}
        </div>

        {/* Content Table */}
        <div className="AdminContentManagement__table">
          <div className="table-container">
            <table className="content-table">
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={selectedContent.length === filteredContent.length && filteredContent.length > 0}
                    />
                  </th>
                  <th>Content</th>
                  <th>Seller</th>
                  <th>Category</th>
                  <th>Price</th>
                  <th>Status</th>
                  <th>Upload Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredContent.map((item) => (
                  <tr key={item.id}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedContent.includes(item.id)}
                        onChange={() => handleSelectContent(item.id)}
                      />
                    </td>
                    <td>
                      <div className="content-info">
                        <div className="content-thumbnail">
                          {item.thumbnail ? (
                            <img src={item.thumbnail} alt={item.title} />
                          ) : (
                            <FaVideo />
                          )}
                        </div>
                        <div className="content-details">
                          <span className="content-title">{item.title}</span>
                        </div>
                      </div>
                    </td>
                    <td>{item.seller}</td>
                    <td>
                      <span className={getCategoryBadge(item.category)}>
                        {item.category}
                      </span>
                    </td>
                    <td>{formatCurrency(item.price)}</td>
                    <td>
                      <span className={getStatusBadge(item.status)}>
                        {item.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td>{formatDate(item.uploadDate)}</td>
                    <td>
                      <div className="table-actions">
                        <button
                          className="btn-action view"
                          title="View Content"
                          onClick={() => handleContentAction(item, 'view')}
                        >
                          <FaEye />
                        </button>
                        {item.status === 'under_review' && (
                          <>
                            <button
                              className="btn-action approve"
                              title="Approve Content"
                              onClick={() => handleContentAction(item, 'approve')}
                            >
                              <FaCheck />
                            </button>
                            <button
                              className="btn-action reject"
                              title="Reject Content"
                              onClick={() => handleContentAction(item, 'reject')}
                            >
                              <FaTimes />
                            </button>
                          </>
                        )}
                        <button
                          className="btn-action edit"
                          title="Edit Content"
                          onClick={() => handleContentAction(item, 'edit')}
                        >
                          <FaEdit />
                        </button>
                        <button
                          className="btn-action delete"
                          title="Delete Content"
                          onClick={() => handleContentAction(item, 'delete')}
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredContent.length === 0 && (
            <div className="no-results">
              <FaVideo className="no-results-icon" />
              <h3>No content found</h3>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="AdminContentManagement__pagination">
          <div className="pagination-info">
            Showing {filteredContent.length} of {content.length} content items
          </div>
          <div className="pagination-controls">
            <button className="btn btn-outline" disabled>Previous</button>
            <span className="page-number active">1</span>
            <button className="btn btn-outline" disabled>Next</button>
          </div>
        </div>

        {/* Modals */}
        {ui.showContentDetailModal && <ContentDetailModal />}
        <AddContentModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
        />
      </div>
    </AdminLayout>
  );
};

export default AdminContentManagement;
