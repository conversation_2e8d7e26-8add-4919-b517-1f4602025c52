import React, { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import "../../styles/Auth.css";
import { FaMobile } from "react-icons/fa6";
import { login, reset, googleSignIn } from "../../redux/slices/authSlice";
import { APP_CONFIG } from "../../utils/constants";
import toast from "../../utils/toast";
import GoogleSignInButton from "../../components/common/GoogleSignInButton";
import RoleSelectionModal from "../../components/common/RoleSelectionModal";
import firebaseService from "../../services/firebaseService";
import { getSellerRedirectPath } from "../../utils/sellerUtils";

const Auth = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLoading, isError, isSuccess, error } = useSelector(
    (state) => state.auth
  );

  const [formData, setFormData] = useState({
    phone: "",
    countryCode: "+91",
  });

  const [errors, setErrors] = useState({});
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [googleUserInfo, setGoogleUserInfo] = useState(null);
  const [googleIdToken, setGoogleIdToken] = useState(null);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const handleCountryCodeChange = (e) => {
    setFormData({
      ...formData,
      countryCode: e.target.value,
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = "Phone number must be 10 digits";
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    dispatch(reset());

    try {
      // Prepare mobile number with country code
      const mobile = `${formData.countryCode}${formData.phone}`;

      // Dispatch login action
      const result = await dispatch(login({ mobile })).unwrap();

      // Show success message
      toast.otp.success("OTP sent successfully!");

      // Navigate to OTP verification page with user ID and phone number
      navigate("/otp-verification", {
        state: {
          userId: result.userId,
          phoneNumber: `${formData.countryCode} ${formData.phone}`,
          cooldownSeconds: result.cooldownSeconds || 60,
          isLogin: true,
          developmentOtp: result.developmentOtp, // Pass development OTP if available
        },
      });
    } catch (error) {
      // Handle different types of errors
      if (error.includes("wait") && error.includes("seconds")) {
        // Cooldown error
        const seconds = error.match(/\d+/)?.[0] || 60;
        toast.otp.cooldown(parseInt(seconds));
      } else {
        // Other errors
        toast.api.error({ response: { data: { message: error } } });
      }
    }
  };

  // Handle Google Sign-In
  const handleGoogleSignIn = async () => {
    try {
      dispatch(reset());

      // Check if Firebase is initialized
      if (!firebaseService.isInitialized()) {
        toast.error(
          "Firebase is not initialized. Please check your configuration."
        );
        return;
      }

      // Sign in with Google using Firebase
      const result = await firebaseService.signInWithGoogle();

      // Try to sign in with existing account
      try {
        const response = await dispatch(googleSignIn(result.idToken)).unwrap();

        // Success - user exists and is logged in
        toast.auth.loginSuccess();

        // Navigate based on user role and onboarding status
        if (response.user.role === "buyer") {
          navigate("/buyer/dashboard");
        } else if (response.user.role === "seller") {
          // For sellers, check onboarding status and redirect accordingly
          const redirectPath = getSellerRedirectPath(response.user);
          navigate(redirectPath);
        } else if (response.user.role === "admin") {
          navigate("/admin/dashboard");
        } else {
          navigate("/");
        }
      } catch (signInError) {
        // User doesn't exist - show role selection for sign-up
        const errorMessage =
          typeof signInError === "string"
            ? signInError
            : signInError?.message || "";
        if (
          errorMessage.includes("not found") ||
          errorMessage.includes("does not exist")
        ) {
          setGoogleUserInfo(result.user);
          setGoogleIdToken(result.idToken);
          setShowRoleModal(true);
        } else {
          throw signInError;
        }
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
      const errorMessage =
        typeof error === "string"
          ? error
          : error?.message ||
            "Failed to sign in with Google. Please try again.";
      toast.error(errorMessage);
    }
  };

  // Handle role selection for Google Sign-Up
  const handleRoleSelect = async (role) => {
    try {
      const { googleSignUp } = await import("../../redux/slices/authSlice");

      const response = await dispatch(
        googleSignUp({ idToken: googleIdToken, role })
      ).unwrap();

      // Success - user created and logged in
      toast.auth.registrationSuccess();
      setShowRoleModal(false);

      // Navigate based on selected role and onboarding status
      if (role === "buyer") {
        navigate("/buyer/dashboard");
      } else if (role === "seller") {
        // For new sellers, redirect to onboarding (they won't have completed it yet)
        navigate("/seller-onboarding");
      } else {
        navigate("/");
      }
    } catch (error) {
      console.error("Google sign-up error:", error);
      toast.error(
        typeof error === "string"
          ? error
          : "Failed to complete registration. Please try again."
      );
    }
  };

  // Handle role modal close
  const handleRoleModalClose = () => {
    setShowRoleModal(false);
    setGoogleUserInfo(null);
    setGoogleIdToken(null);
  };

  return (
    <div className="auth-page auth-container">
      <div className="auth-form-container">
        <h1 className="auth-title">Login in to your account</h1>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-input form-input-container">
            <div className="phone-input-wrapper">
              <div>
                <div className="country-code-select">
                  <FaMobile style={{ color: "var(--dark-gray)" }} />
                  <select
                    value={formData.countryCode}
                    onChange={handleCountryCodeChange}
                    className="selectstylesnone"
                  >
                    <option value="+91">+91</option>
                    <option value="+1">+1</option>
                    <option value="+44">+44</option>
                    <option value="+61">+61</option>
                    <option value="+86">+86</option>
                    <option value="+49">+49</option>
                    <option value="+33">+33</option>
                    <option value="+81">+81</option>
                    <option value="+7">+7</option>
                    <option value="+55">+55</option>
                  </select>
                </div>
              </div>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={(e) => {
                  const phoneValue = e.target.value.replace(/\D/g, "");
                  handleChange({
                    target: {
                      name: "phone",
                      value: phoneValue,
                    },
                  });
                }}
                placeholder="Enter Phone Number"
                className={`form-input phone-input ${
                  errors.phone ? "input-error" : ""
                }`}
                required
                pattern="[0-9]*"
              />
            </div>
            {errors.phone && <p className="error-message">{errors.phone}</p>}
          </div>

          <button type="submit" className="signin-button" disabled={isLoading}>
            {isLoading ? "Sending OTP..." : "Sign In"}
          </button>

          <div className="auth-divider">
            <span>or</span>
          </div>

          <GoogleSignInButton
            onClick={handleGoogleSignIn}
            isLoading={isLoading}
            text="Sign in with Google"
            variant="secondary"
          />

          <p className="signup-link mt-10">
            Don't have an account? <Link to="/signup">Sign Up</Link>
          </p>
        </form>
      </div>

      {/* Role Selection Modal for Google Sign-Up */}
      <RoleSelectionModal
        isOpen={showRoleModal}
        onClose={handleRoleModalClose}
        onRoleSelect={handleRoleSelect}
        userInfo={googleUserInfo}
        isLoading={isLoading}
      />
    </div>
  );
};

export default Auth;
