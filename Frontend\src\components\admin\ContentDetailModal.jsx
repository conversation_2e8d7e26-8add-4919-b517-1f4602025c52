import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentContentDetail,
  selectLoading,
  hideContentDetailModal,
  updateContent,
  deleteContent,
  approveContent,
  rejectContent,
  setContentLoading,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import "../../styles/ContentDetailModal.css";

// Icons
import { 
  FaTimes, FaVideo, FaUser, FaCalendarAlt, FaDollarSign, 
  FaEdit, FaTrash, FaCheck, FaEye, FaDownload, FaFlag 
} from "react-icons/fa";
import { MdCategory, MdDescription } from "react-icons/md";

const ContentDetailModal = () => {
  const dispatch = useDispatch();
  const currentContent = useSelector(selectCurrentContentDetail);
  const loading = useSelector(selectLoading);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({});

  if (!currentContent) return null;

  const handleClose = () => {
    dispatch(hideContentDetailModal());
    setIsEditing(false);
    setEditForm({});
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditForm({
      title: currentContent.title,
      description: currentContent.description,
      category: currentContent.category,
      price: currentContent.price,
      status: currentContent.status,
    });
  };

  const handleSave = async () => {
    dispatch(setContentLoading(true));
    
    // Simulate API call
    setTimeout(() => {
      dispatch(updateContent({ id: currentContent.id, ...editForm }));
      dispatch(addActivity({
        id: Date.now(),
        type: 'content_update',
        description: `Content updated: ${editForm.title}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));
      dispatch(setContentLoading(false));
      setIsEditing(false);
      
      // Show success message
      alert(`Content "${editForm.title}" has been updated successfully!`);
    }, 1000);
  };

  const handleApprove = () => {
    if (window.confirm(`Are you sure you want to approve "${currentContent.title}"?`)) {
      dispatch(approveContent(currentContent.id));
      dispatch(addActivity({
        id: Date.now(),
        type: 'content_approval',
        description: `Content approved: ${currentContent.title}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));
      alert(`Content "${currentContent.title}" has been approved successfully!`);
    }
  };

  const handleReject = () => {
    const reason = prompt(`Please provide a reason for rejecting "${currentContent.title}":`);
    if (reason) {
      dispatch(rejectContent(currentContent.id));
      dispatch(addActivity({
        id: Date.now(),
        type: 'content_rejection',
        description: `Content rejected: ${currentContent.title} - Reason: ${reason}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));
      alert(`Content "${currentContent.title}" has been rejected.`);
    }
  };

  const handleDelete = () => {
    if (window.confirm(`Are you sure you want to delete "${currentContent.title}"? This action cannot be undone.`)) {
      dispatch(deleteContent(currentContent.id));
      dispatch(addActivity({
        id: Date.now(),
        type: 'content_deletion',
        description: `Content deleted: ${currentContent.title}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));
      handleClose();
      alert(`Content "${currentContent.title}" has been deleted successfully!`);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { class: 'status-pending', label: 'Pending Review' },
      approved: { class: 'status-approved', label: 'Approved' },
      rejected: { class: 'status-rejected', label: 'Rejected' },
      draft: { class: 'status-draft', label: 'Draft' },
    };
    
    const config = statusConfig[status] || statusConfig.draft;
    return <span className={`status-badge ${config.class}`}>{config.label}</span>;
  };

  return (
    <div className="ContentDetailModal">
      <div className="ContentDetailModal__overlay" onClick={handleClose} />
      <div className="ContentDetailModal__container">
        {/* Header */}
        <div className="ContentDetailModal__header">
          <div className="header-content">
            <div className="content-thumbnail">
              {currentContent.thumbnail ? (
                <img src={currentContent.thumbnail} alt={currentContent.title} />
              ) : (
                <FaVideo />
              )}
            </div>
            <div className="content-basic-info">
              <h2>{currentContent.title}</h2>
              <div className="content-badges">
                {getStatusBadge(currentContent.status)}
                <span className="category-badge">{currentContent.category}</span>
              </div>
            </div>
          </div>
          <button className="close-btn" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="ContentDetailModal__content">
          {/* Content Information */}
          <div className="info-section">
            <div className="section-header">
              <h3>Content Information</h3>
              {!isEditing && currentContent.status === 'pending' && (
                <button className="btn btn-outline" onClick={handleEdit}>
                  <FaEdit />
                  Edit Content
                </button>
              )}
            </div>

            {isEditing ? (
              <div className="edit-form">
                <div className="form-group">
                  <label>Title</label>
                  <input
                    type="text"
                    value={editForm.title}
                    onChange={(e) => setEditForm({...editForm, title: e.target.value})}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label>Description</label>
                  <textarea
                    value={editForm.description}
                    onChange={(e) => setEditForm({...editForm, description: e.target.value})}
                    className="form-textarea"
                    rows="4"
                  />
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label>Category</label>
                    <select
                      value={editForm.category}
                      onChange={(e) => setEditForm({...editForm, category: e.target.value})}
                      className="form-select"
                    >
                      <option value="Football">Football</option>
                      <option value="Basketball">Basketball</option>
                      <option value="Baseball">Baseball</option>
                      <option value="Soccer">Soccer</option>
                      <option value="Tennis">Tennis</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Price ($)</label>
                    <input
                      type="number"
                      value={editForm.price}
                      onChange={(e) => setEditForm({...editForm, price: parseFloat(e.target.value)})}
                      className="form-input"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>
                <div className="form-actions">
                  <button 
                    className="btn btn-primary"
                    onClick={handleSave}
                    disabled={loading.content}
                  >
                    {loading.content ? "Saving..." : "Save Changes"}
                  </button>
                  <button 
                    className="btn btn-outline"
                    onClick={() => setIsEditing(false)}
                    disabled={loading.content}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="info-grid">
                <div className="info-item">
                  <MdDescription className="info-icon" />
                  <div>
                    <span className="info-label">Description</span>
                    <span className="info-value">{currentContent.description}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaUser className="info-icon" />
                  <div>
                    <span className="info-label">Seller</span>
                    <span className="info-value">{currentContent.seller}</span>
                  </div>
                </div>
                <div className="info-item">
                  <MdCategory className="info-icon" />
                  <div>
                    <span className="info-label">Category</span>
                    <span className="info-value">{currentContent.category}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaDollarSign className="info-icon" />
                  <div>
                    <span className="info-label">Price</span>
                    <span className="info-value">${currentContent.price}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaCalendarAlt className="info-icon" />
                  <div>
                    <span className="info-label">Upload Date</span>
                    <span className="info-value">{new Date(currentContent.uploadDate).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaEye className="info-icon" />
                  <div>
                    <span className="info-label">Views</span>
                    <span className="info-value">{currentContent.views || 0}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="actions-section">
            <div className="action-buttons">
              {currentContent.status === 'pending' && (
                <>
                  <button 
                    className="btn btn-success"
                    onClick={handleApprove}
                    disabled={loading.content}
                  >
                    <FaCheck />
                    Approve Content
                  </button>
                  <button 
                    className="btn btn-warning"
                    onClick={handleReject}
                    disabled={loading.content}
                  >
                    <FaFlag />
                    Reject Content
                  </button>
                </>
              )}
              <button 
                className="btn btn-outline"
                onClick={() => window.open(currentContent.fileUrl, '_blank')}
              >
                <FaDownload />
                Download File
              </button>
              <button 
                className="btn btn-danger"
                onClick={handleDelete}
                disabled={loading.content}
              >
                <FaTrash />
                Delete Content
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentDetailModal;
