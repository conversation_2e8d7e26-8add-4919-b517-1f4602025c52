import { createSlice } from '@reduxjs/toolkit';

// Initial state for the admin dashboard
const initialState = {
  // Sidebar state
  activeTab: 'dashboard', // Default active tab
  isSidebarOpen: false,

  // Admin profile data (mock data for development)
  profile: {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+****************',
    profileImage: null,
    role: 'admin',
  },

  // Dashboard statistics
  stats: {
    totalBuyers: 1247,
    totalSellers: 89,
    totalContent: 456,
    totalRevenue: 12450.00,
    pendingApprovals: 23,
    activeUsers: 1156,
    monthlyRevenue: 3200.00,
    monthlyOrders: 156,
  },

  // Loading states
  loading: {
    stats: false,
    users: false,
    content: false,
    reports: false,
    cms: false,
    settings: false,
    approval: false,
    userDetail: false,
    contentDetail: false,
  },

  // Error states
  errors: {
    stats: null,
    users: null,
    content: null,
    reports: null,
    cms: null,
    settings: null,
    approval: null,
    userDetail: null,
    contentDetail: null,
  },

  // UI states
  ui: {
    selectedUsers: [],
    selectedContent: [],
    selectedCMSPages: [],
    showApprovalModal: false,
    showUserDetailModal: false,
    showContentDetailModal: false,
    showCMSEditorModal: false,
    currentApprovalItem: null,
    currentUserDetail: null,
    currentContentDetail: null,
    currentCMSPage: null,
    bulkActionType: null,
  },

  // Financial settings
  financialSettings: {
    platformCommission: 15,
    sellerPayout: 85,
    minimumPayout: 50,
    processingFee: 2.9,
    payoutSchedule: 'weekly',
    taxRate: 0,
    autoApprove: false,
    emailVerificationRequired: true,
    phoneVerificationRequired: false,
    maxFileSize: 100, // MB
    allowedFormats: ['mp4', 'pdf', 'zip'],
    minPrice: 5,
    maxPrice: 500,
  },

  // Users data
  users: [
    {
      id: 1,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'buyer',
      status: 'active',
      dateJoined: '2024-01-15',
      lastLogin: '2024-01-20',
      profileImage: null,
      totalPurchases: 12,
      totalSpent: 450.00,
      verificationStatus: 'verified',
      activityHistory: [
        { date: '2024-01-20', action: 'Purchased Basketball Training Fundamentals', amount: 29.99 },
        { date: '2024-01-19', action: 'Logged in', amount: null },
        { date: '2024-01-18', action: 'Updated profile', amount: null },
      ],
    },
    {
      id: 2,
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      role: 'seller',
      status: 'active',
      dateJoined: '2024-01-10',
      profileImage: null,
    },
    {
      id: 3,
      firstName: 'Mike',
      lastName: 'Johnson',
      email: '<EMAIL>',
      role: 'buyer',
      status: 'inactive',
      dateJoined: '2024-01-08',
      profileImage: null,
    },
    {
      id: 4,
      firstName: 'Sarah',
      lastName: 'Wilson',
      email: '<EMAIL>',
      role: 'seller',
      status: 'active',
      dateJoined: '2024-01-05',
      profileImage: null,
    },
    {
      id: 5,
      firstName: 'David',
      lastName: 'Brown',
      email: '<EMAIL>',
      role: 'buyer',
      status: 'active',
      dateJoined: '2024-01-03',
      profileImage: null,
    },
  ],

  // Content/Products data
  content: [
    {
      id: 1,
      title: 'Basketball Training Fundamentals',
      seller: 'Jane Smith',
      category: 'Training Videos',
      price: 29.99,
      status: 'published',
      uploadDate: '2024-01-20',
      thumbnail: null,
    },
    {
      id: 2,
      title: 'Soccer Tactics Masterclass',
      seller: 'Sarah Wilson',
      category: 'Courses',
      price: 39.99,
      status: 'under_review',
      uploadDate: '2024-01-19',
      thumbnail: null,
    },
    {
      id: 3,
      title: 'Tennis Serve Techniques',
      seller: 'Jane Smith',
      category: 'Training Videos',
      price: 24.99,
      status: 'published',
      uploadDate: '2024-01-18',
      thumbnail: null,
    },
    {
      id: 4,
      title: 'Football Strategy Guide',
      seller: 'Sarah Wilson',
      category: 'E-books',
      price: 19.99,
      status: 'draft',
      uploadDate: '2024-01-17',
      thumbnail: null,
    },
    {
      id: 5,
      title: 'Baseball Pitching Masterclass',
      seller: 'Jane Smith',
      category: 'Courses',
      price: 49.99,
      status: 'under_review',
      uploadDate: '2024-01-16',
      thumbnail: null,
    },
  ],

  // Pending approvals
  pendingApprovals: [
    {
      id: 1,
      contentTitle: 'Soccer Tactics Masterclass',
      seller: 'Sarah Wilson',
      category: 'Courses',
      submissionDate: '2024-01-19',
      type: 'content',
    },
    {
      id: 2,
      contentTitle: 'Baseball Pitching Masterclass',
      seller: 'Jane Smith',
      category: 'Courses',
      submissionDate: '2024-01-16',
      type: 'content',
    },
    {
      id: 3,
      contentTitle: 'Swimming Techniques',
      seller: 'Mike Johnson',
      category: 'Training Videos',
      submissionDate: '2024-01-15',
      type: 'content',
    },
    {
      id: 4,
      contentTitle: 'Golf Swing Analysis',
      seller: 'David Brown',
      category: 'Courses',
      submissionDate: '2024-01-14',
      type: 'content',
    },
    {
      id: 5,
      contentTitle: 'Volleyball Strategies',
      seller: 'Sarah Wilson',
      category: 'E-books',
      submissionDate: '2024-01-13',
      type: 'content',
    },
  ],

  // Recent activity
  recentActivity: [
    {
      id: 1,
      type: 'user_registration',
      description: 'New buyer registered: John Doe',
      timestamp: '2024-01-20 10:30:00',
      user: 'John Doe',
    },
    {
      id: 2,
      type: 'content_upload',
      description: 'New content uploaded: Basketball Training Fundamentals',
      timestamp: '2024-01-20 09:15:00',
      user: 'Jane Smith',
    },
    {
      id: 3,
      type: 'purchase',
      description: 'Content purchased: Soccer Tactics Masterclass',
      timestamp: '2024-01-19 16:45:00',
      user: 'Mike Johnson',
    },
    {
      id: 4,
      type: 'seller_verification',
      description: 'Seller verified: Sarah Wilson',
      timestamp: '2024-01-19 14:20:00',
      user: 'Sarah Wilson',
    },
    {
      id: 5,
      type: 'content_approval',
      description: 'Content approved: Tennis Serve Techniques',
      timestamp: '2024-01-18 11:30:00',
      user: 'Jane Smith',
    },
  ],

  // CMS Pages data
  cmsPages: [
    {
      id: 1,
      title: 'About Us',
      slug: 'about-us',
      status: 'published',
      lastModified: '2024-01-15',
    },
    {
      id: 2,
      title: 'Terms of Service',
      slug: 'terms-of-service',
      status: 'published',
      lastModified: '2024-01-10',
    },
    {
      id: 3,
      title: 'Privacy Policy',
      slug: 'privacy-policy',
      status: 'published',
      lastModified: '2024-01-08',
    },
    {
      id: 4,
      title: 'FAQ',
      slug: 'faq',
      status: 'draft',
      lastModified: '2024-01-05',
    },
    {
      id: 5,
      title: 'Contact Us',
      slug: 'contact-us',
      status: 'published',
      lastModified: '2024-01-03',
    },
  ],

  // Analytics data for charts
  analytics: {
    salesChart: {
      labels: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      data: [1200, 1900, 3000, 2500, 2200, 3200],
    },
    userRegistrations: {
      labels: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      data: [45, 67, 89, 123, 156, 189],
    },
    categoryDistribution: {
      labels: ['Training Videos', 'Courses', 'E-books', 'Live Sessions'],
      data: [35, 25, 20, 20],
    },
    revenueByCategory: {
      labels: ['Training Videos', 'Courses', 'E-books', 'Live Sessions'],
      data: [4500, 3200, 2100, 2650],
    },
  },
};

// Admin dashboard slice
const adminDashboardSlice = createSlice({
  name: 'adminDashboard',
  initialState,
  reducers: {
    // Sidebar actions
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    toggleSidebar: (state) => {
      state.isSidebarOpen = !state.isSidebarOpen;
    },
    setSidebarOpen: (state, action) => {
      state.isSidebarOpen = action.payload;
    },

    // Profile actions
    updateProfile: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
    },

    // Stats actions
    updateStats: (state, action) => {
      state.stats = { ...state.stats, ...action.payload };
    },
    setStatsLoading: (state, action) => {
      state.loading.stats = action.payload;
    },
    setStatsError: (state, action) => {
      state.errors.stats = action.payload;
    },

    // Users actions
    addUser: (state, action) => {
      state.users.push(action.payload);
    },
    updateUser: (state, action) => {
      const index = state.users.findIndex(user => user.id === action.payload.id);
      if (index !== -1) {
        state.users[index] = { ...state.users[index], ...action.payload };
      }
    },
    deleteUser: (state, action) => {
      state.users = state.users.filter(user => user.id !== action.payload);
    },
    setUsersLoading: (state, action) => {
      state.loading.users = action.payload;
    },
    setUsersError: (state, action) => {
      state.errors.users = action.payload;
    },

    // Content actions
    addContent: (state, action) => {
      state.content.push(action.payload);
    },
    updateContent: (state, action) => {
      const index = state.content.findIndex(item => item.id === action.payload.id);
      if (index !== -1) {
        state.content[index] = { ...state.content[index], ...action.payload };
      }
    },
    deleteContent: (state, action) => {
      state.content = state.content.filter(item => item.id !== action.payload);
    },
    approveContent: (state, action) => {
      const index = state.content.findIndex(item => item.id === action.payload);
      if (index !== -1) {
        state.content[index].status = 'published';
      }
      // Remove from pending approvals
      state.pendingApprovals = state.pendingApprovals.filter(
        item => item.id !== action.payload
      );
    },
    rejectContent: (state, action) => {
      const index = state.content.findIndex(item => item.id === action.payload);
      if (index !== -1) {
        state.content[index].status = 'rejected';
      }
      // Remove from pending approvals
      state.pendingApprovals = state.pendingApprovals.filter(
        item => item.id !== action.payload
      );
    },
    setContentLoading: (state, action) => {
      state.loading.content = action.payload;
    },
    setContentError: (state, action) => {
      state.errors.content = action.payload;
    },

    // CMS Pages actions
    addCMSPage: (state, action) => {
      state.cmsPages.push(action.payload);
    },
    updateCMSPage: (state, action) => {
      const index = state.cmsPages.findIndex(page => page.id === action.payload.id);
      if (index !== -1) {
        state.cmsPages[index] = { ...state.cmsPages[index], ...action.payload };
      }
    },
    deleteCMSPage: (state, action) => {
      state.cmsPages = state.cmsPages.filter(page => page.id !== action.payload);
    },
    setCMSLoading: (state, action) => {
      state.loading.cms = action.payload;
    },
    setCMSError: (state, action) => {
      state.errors.cms = action.payload;
    },

    // Activity actions
    addActivity: (state, action) => {
      state.recentActivity.unshift(action.payload);
      // Keep only the latest 10 activities
      if (state.recentActivity.length > 10) {
        state.recentActivity = state.recentActivity.slice(0, 10);
      }
    },

    // Analytics actions
    updateAnalytics: (state, action) => {
      state.analytics = { ...state.analytics, ...action.payload };
    },

    // Clear all errors
    clearErrors: (state) => {
      state.errors = {
        stats: null,
        users: null,
        content: null,
        reports: null,
        cms: null,
        settings: null,
      };
    },

    // Reset dashboard state
    resetDashboard: () => {
      return initialState;
    },

    // UI Actions
    setSelectedUsers: (state, action) => {
      state.ui.selectedUsers = action.payload;
    },
    setSelectedContent: (state, action) => {
      state.ui.selectedContent = action.payload;
    },
    setSelectedCMSPages: (state, action) => {
      state.ui.selectedCMSPages = action.payload;
    },
    showApprovalModal: (state, action) => {
      state.ui.showApprovalModal = true;
      state.ui.currentApprovalItem = action.payload;
    },
    hideApprovalModal: (state) => {
      state.ui.showApprovalModal = false;
      state.ui.currentApprovalItem = null;
    },
    showUserDetailModal: (state, action) => {
      state.ui.showUserDetailModal = true;
      state.ui.currentUserDetail = action.payload;
    },
    hideUserDetailModal: (state) => {
      state.ui.showUserDetailModal = false;
      state.ui.currentUserDetail = null;
    },
    showContentDetailModal: (state, action) => {
      state.ui.showContentDetailModal = true;
      state.ui.currentContentDetail = action.payload;
    },
    hideContentDetailModal: (state) => {
      state.ui.showContentDetailModal = false;
      state.ui.currentContentDetail = null;
    },
    showCMSEditorModal: (state, action) => {
      state.ui.showCMSEditorModal = true;
      state.ui.currentCMSPage = action.payload;
    },
    hideCMSEditorModal: (state) => {
      state.ui.showCMSEditorModal = false;
      state.ui.currentCMSPage = null;
    },
    setBulkActionType: (state, action) => {
      state.ui.bulkActionType = action.payload;
    },

    // Financial Settings Actions
    updateFinancialSettings: (state, action) => {
      state.financialSettings = { ...state.financialSettings, ...action.payload };
    },

    // Bulk User Actions
    bulkUpdateUsers: (state, action) => {
      const { userIds, updates } = action.payload;
      state.users = state.users.map(user =>
        userIds.includes(user.id) ? { ...user, ...updates } : user
      );
    },
    bulkDeleteUsers: (state, action) => {
      state.users = state.users.filter(user => !action.payload.includes(user.id));
    },

    // Bulk Content Actions
    bulkUpdateContent: (state, action) => {
      const { contentIds, updates } = action.payload;
      state.content = state.content.map(item =>
        contentIds.includes(item.id) ? { ...item, ...updates } : item
      );
    },
    bulkDeleteContent: (state, action) => {
      state.content = state.content.filter(item => !action.payload.includes(item.id));
    },

    // Loading Actions
    setApprovalLoading: (state, action) => {
      state.loading.approval = action.payload;
    },
    setUserDetailLoading: (state, action) => {
      state.loading.userDetail = action.payload;
    },
    setContentDetailLoading: (state, action) => {
      state.loading.contentDetail = action.payload;
    },

    // Error Actions
    setApprovalError: (state, action) => {
      state.errors.approval = action.payload;
    },
    setUserDetailError: (state, action) => {
      state.errors.userDetail = action.payload;
    },
    setContentDetailError: (state, action) => {
      state.errors.contentDetail = action.payload;
    },
  },
});

// Export actions
export const {
  setActiveTab,
  toggleSidebar,
  setSidebarOpen,
  updateProfile,
  updateStats,
  setStatsLoading,
  setStatsError,
  addUser,
  updateUser,
  deleteUser,
  setUsersLoading,
  setUsersError,
  addContent,
  updateContent,
  deleteContent,
  approveContent,
  rejectContent,
  setContentLoading,
  setContentError,
  addCMSPage,
  updateCMSPage,
  deleteCMSPage,
  setCMSLoading,
  setCMSError,
  addActivity,
  updateAnalytics,
  clearErrors,
  resetDashboard,
  // UI Actions
  setSelectedUsers,
  setSelectedContent,
  setSelectedCMSPages,
  showApprovalModal,
  hideApprovalModal,
  showUserDetailModal,
  hideUserDetailModal,
  showContentDetailModal,
  hideContentDetailModal,
  showCMSEditorModal,
  hideCMSEditorModal,
  setBulkActionType,
  // Financial Settings
  updateFinancialSettings,
  // Bulk Actions
  bulkUpdateUsers,
  bulkDeleteUsers,
  bulkUpdateContent,
  bulkDeleteContent,
  // Loading Actions
  setApprovalLoading,
  setUserDetailLoading,
  setContentDetailLoading,
  // Error Actions
  setApprovalError,
  setUserDetailError,
  setContentDetailError,
} = adminDashboardSlice.actions;

// Selectors
export const selectActiveTab = (state) => state.adminDashboard.activeTab;
export const selectIsSidebarOpen = (state) => state.adminDashboard.isSidebarOpen;
export const selectProfile = (state) => state.adminDashboard.profile;
export const selectStats = (state) => state.adminDashboard.stats;
export const selectUsers = (state) => state.adminDashboard.users;
export const selectContent = (state) => state.adminDashboard.content;
export const selectPendingApprovals = (state) => state.adminDashboard.pendingApprovals;
export const selectRecentActivity = (state) => state.adminDashboard.recentActivity;
export const selectCMSPages = (state) => state.adminDashboard.cmsPages;
export const selectAnalytics = (state) => state.adminDashboard.analytics;
export const selectLoading = (state) => state.adminDashboard.loading;
export const selectErrors = (state) => state.adminDashboard.errors;
export const selectUI = (state) => state.adminDashboard.ui;
export const selectFinancialSettings = (state) => state.adminDashboard.financialSettings;
export const selectSelectedUsers = (state) => state.adminDashboard.ui.selectedUsers;
export const selectSelectedContent = (state) => state.adminDashboard.ui.selectedContent;
export const selectSelectedCMSPages = (state) => state.adminDashboard.ui.selectedCMSPages;
export const selectCurrentApprovalItem = (state) => state.adminDashboard.ui.currentApprovalItem;
export const selectCurrentUserDetail = (state) => state.adminDashboard.ui.currentUserDetail;
export const selectCurrentContentDetail = (state) => state.adminDashboard.ui.currentContentDetail;
export const selectCurrentCMSPage = (state) => state.adminDashboard.ui.currentCMSPage;

export default adminDashboardSlice.reducer;
