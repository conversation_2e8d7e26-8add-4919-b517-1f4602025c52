import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentUserDetail,
  selectLoading,
  hideUserDetailModal,
  updateUser,
  deleteUser,
  setUserDetailLoading,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import "../../styles/UserDetailModal.css";

// Icons
import { 
  FaTimes, FaUser, FaEnvelope, FaPhone, FaCalendarAlt, 
  FaDollarSign, FaShoppingCart, FaEdit, FaTrash, FaToggleOn, FaToggleOff,
  FaCheckCircle, FaTimesCircle 
} from "react-icons/fa";
import { MdVerified, MdBlock } from "react-icons/md";

const UserDetailModal = () => {
  const dispatch = useDispatch();
  const currentUser = useSelector(selectCurrentUserDetail);
  const loading = useSelector(selectLoading);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({});

  if (!currentUser) return null;

  const handleClose = () => {
    dispatch(hideUserDetailModal());
    setIsEditing(false);
    setEditForm({});
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditForm({
      firstName: currentUser.firstName,
      lastName: currentUser.lastName,
      email: currentUser.email,
      phone: currentUser.phone,
      role: currentUser.role,
      status: currentUser.status,
    });
  };

  const handleSave = async () => {
    dispatch(setUserDetailLoading(true));
    
    // Simulate API call
    setTimeout(() => {
      dispatch(updateUser({ id: currentUser.id, ...editForm }));
      dispatch(addActivity({
        id: Date.now(),
        type: 'user_update',
        description: `User updated: ${editForm.firstName} ${editForm.lastName}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));
      dispatch(setUserDetailLoading(false));
      setIsEditing(false);
      
      // Show success message
      alert(`User "${editForm.firstName} ${editForm.lastName}" has been updated successfully!`);
    }, 1000);
  };

  const handleToggleStatus = async () => {
    const newStatus = currentUser.status === 'active' ? 'inactive' : 'active';
    dispatch(setUserDetailLoading(true));
    
    // Simulate API call
    setTimeout(() => {
      dispatch(updateUser({ id: currentUser.id, status: newStatus }));
      dispatch(addActivity({
        id: Date.now(),
        type: 'user_status_change',
        description: `User ${newStatus}: ${currentUser.firstName} ${currentUser.lastName}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));
      dispatch(setUserDetailLoading(false));
      
      // Show success message
      alert(`User has been ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully!`);
    }, 1000);
  };

  const handleDelete = async () => {
    if (!window.confirm(`Are you sure you want to delete user "${currentUser.firstName} ${currentUser.lastName}"? This action cannot be undone.`)) {
      return;
    }

    dispatch(setUserDetailLoading(true));
    
    // Simulate API call
    setTimeout(() => {
      dispatch(deleteUser(currentUser.id));
      dispatch(addActivity({
        id: Date.now(),
        type: 'user_deletion',
        description: `User deleted: ${currentUser.firstName} ${currentUser.lastName}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));
      dispatch(setUserDetailLoading(false));
      handleClose();
      
      // Show success message
      alert(`User "${currentUser.firstName} ${currentUser.lastName}" has been deleted successfully!`);
    }, 1000);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusIcon = (status) => {
    return status === 'active' ? 
      <FaCheckCircle className="status-icon active" /> : 
      <FaTimesCircle className="status-icon inactive" />;
  };

  const getRoleBadge = (role) => {
    const badges = {
      buyer: { color: '#3b82f6', label: 'Buyer' },
      seller: { color: '#10b981', label: 'Seller' },
      admin: { color: '#f59e0b', label: 'Admin' },
    };
    const badge = badges[role] || badges.buyer;
    
    return (
      <span 
        className="role-badge" 
        style={{ backgroundColor: `${badge.color}20`, color: badge.color }}
      >
        {badge.label}
      </span>
    );
  };

  return (
    <div className="UserDetailModal">
      <div className="UserDetailModal__overlay" onClick={handleClose} />
      <div className="UserDetailModal__container">
        {/* Header */}
        <div className="UserDetailModal__header">
          <div className="header-info">
            <div className="user-avatar">
              {currentUser.profileImage ? (
                <img src={currentUser.profileImage} alt={currentUser.firstName} />
              ) : (
                <FaUser />
              )}
            </div>
            <div className="user-basic-info">
              <h2>{currentUser.firstName} {currentUser.lastName}</h2>
              <div className="user-badges">
                {getRoleBadge(currentUser.role)}
                {getStatusIcon(currentUser.status)}
                {currentUser.verificationStatus === 'verified' && (
                  <MdVerified className="verification-icon verified" title="Verified User" />
                )}
              </div>
            </div>
          </div>
          <button className="close-btn" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="UserDetailModal__content">
          {/* User Information */}
          <div className="info-section">
            <div className="section-header">
              <h3>User Information</h3>
              {!isEditing && (
                <button className="btn btn-outline" onClick={handleEdit}>
                  <FaEdit />
                  Edit User
                </button>
              )}
            </div>

            {isEditing ? (
              <div className="edit-form">
                <div className="form-row">
                  <div className="form-group">
                    <label>First Name</label>
                    <input
                      type="text"
                      value={editForm.firstName}
                      onChange={(e) => setEditForm({...editForm, firstName: e.target.value})}
                      className="form-input"
                    />
                  </div>
                  <div className="form-group">
                    <label>Last Name</label>
                    <input
                      type="text"
                      value={editForm.lastName}
                      onChange={(e) => setEditForm({...editForm, lastName: e.target.value})}
                      className="form-input"
                    />
                  </div>
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label>Email</label>
                    <input
                      type="email"
                      value={editForm.email}
                      onChange={(e) => setEditForm({...editForm, email: e.target.value})}
                      className="form-input"
                    />
                  </div>
                  <div className="form-group">
                    <label>Phone</label>
                    <input
                      type="tel"
                      value={editForm.phone}
                      onChange={(e) => setEditForm({...editForm, phone: e.target.value})}
                      className="form-input"
                    />
                  </div>
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label>Role</label>
                    <select
                      value={editForm.role}
                      onChange={(e) => setEditForm({...editForm, role: e.target.value})}
                      className="form-select"
                    >
                      <option value="buyer">Buyer</option>
                      <option value="seller">Seller</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Status</label>
                    <select
                      value={editForm.status}
                      onChange={(e) => setEditForm({...editForm, status: e.target.value})}
                      className="form-select"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </div>
                <div className="form-actions">
                  <button 
                    className="btn btn-primary"
                    onClick={handleSave}
                    disabled={loading.userDetail}
                  >
                    {loading.userDetail ? "Saving..." : "Save Changes"}
                  </button>
                  <button 
                    className="btn btn-outline"
                    onClick={() => setIsEditing(false)}
                    disabled={loading.userDetail}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="info-grid">
                <div className="info-item">
                  <FaEnvelope className="info-icon" />
                  <div>
                    <span className="info-label">Email</span>
                    <span className="info-value">{currentUser.email}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaPhone className="info-icon" />
                  <div>
                    <span className="info-label">Phone</span>
                    <span className="info-value">{currentUser.phone}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaCalendarAlt className="info-icon" />
                  <div>
                    <span className="info-label">Date Joined</span>
                    <span className="info-value">{formatDate(currentUser.dateJoined)}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaCalendarAlt className="info-icon" />
                  <div>
                    <span className="info-label">Last Login</span>
                    <span className="info-value">{formatDate(currentUser.lastLogin)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Statistics */}
          {currentUser.role === 'buyer' && (
            <div className="stats-section">
              <h3>Purchase Statistics</h3>
              <div className="stats-grid">
                <div className="stat-card">
                  <FaShoppingCart className="stat-icon" />
                  <div className="stat-content">
                    <span className="stat-number">{currentUser.totalPurchases}</span>
                    <span className="stat-label">Total Purchases</span>
                  </div>
                </div>
                <div className="stat-card">
                  <FaDollarSign className="stat-icon" />
                  <div className="stat-content">
                    <span className="stat-number">{formatCurrency(currentUser.totalSpent)}</span>
                    <span className="stat-label">Total Spent</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Activity History */}
          <div className="activity-section">
            <h3>Recent Activity</h3>
            <div className="activity-list">
              {currentUser.activityHistory?.map((activity, index) => (
                <div key={index} className="activity-item">
                  <div className="activity-content">
                    <span className="activity-description">{activity.action}</span>
                    <span className="activity-date">{formatDate(activity.date)}</span>
                  </div>
                  {activity.amount && (
                    <span className="activity-amount">{formatCurrency(activity.amount)}</span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="actions-section">
            <div className="action-buttons">
              <button 
                className={`btn ${currentUser.status === 'active' ? 'btn-warning' : 'btn-success'}`}
                onClick={handleToggleStatus}
                disabled={loading.userDetail}
              >
                {currentUser.status === 'active' ? (
                  <>
                    <MdBlock />
                    Deactivate User
                  </>
                ) : (
                  <>
                    <FaCheckCircle />
                    Activate User
                  </>
                )}
              </button>
              <button 
                className="btn btn-danger"
                onClick={handleDelete}
                disabled={loading.userDetail}
              >
                <FaTrash />
                Delete User
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetailModal;
