import React from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectMyCards,
  selectCardViewMode,
  selectCardForm,
  removeCard,
  setCardViewMode,
  updateCardForm,
  resetCardForm,
  addCard,
} from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import {
  FaPlus,
  FaTrash,
  FaCreditCard,
  FaCalendarAlt,
  FaLock,
} from "react-icons/fa";
import "../../styles/SellerCards.css";

const SellerCards = () => {
  const dispatch = useDispatch();
  const cards = useSelector(selectMyCards);
  const viewMode = useSelector(selectCardViewMode);
  const cardForm = useSelector(selectCardForm);

  // Toggle between list and add views
  const toggleAddCardView = () => {
    dispatch(setCardViewMode(viewMode === "list" ? "add" : "list"));
    if (viewMode === "add") {
      dispatch(resetCardForm());
    }
  };

  // Handle card deletion
  const handleDeleteCard = (cardId) => {
    if (window.confirm("Are you sure you want to remove this card?")) {
      dispatch(removeCard(cardId));
    }
  };

  // Handle form input changes
  const handleNameChange = (value) => {
    dispatch(updateCardForm({ nameOnCard: value }));
  };

  const handleCardNumberChange = (value) => {
    // Remove all non-digits and limit to 16 digits
    const cleanValue = value.replace(/\D/g, "").slice(0, 16);
    // Format with spaces every 4 digits
    const formattedValue = cleanValue.replace(/(\d{4})(?=\d)/g, "$1 ");
    dispatch(updateCardForm({ cardNumber: formattedValue }));
  };

  const handleExpiryChange = (value) => {
    // Remove all non-digits
    const cleanValue = value.replace(/\D/g, "");
    // Format as MM/YY
    let formattedValue = cleanValue;
    if (cleanValue.length >= 2) {
      formattedValue = cleanValue.slice(0, 2) + "/" + cleanValue.slice(2, 4);
    }
    dispatch(updateCardForm({ expiryDate: formattedValue }));
  };

  const handleCvvChange = (value) => {
    dispatch(updateCardForm({ cvv: value }));
  };

  // Handle form submission
  const handleSubmit = () => {
    // Basic validation
    if (!cardForm.nameOnCard || !cardForm.cardNumber || !cardForm.expiryDate || !cardForm.cvv) {
      alert("Please fill in all fields");
      return;
    }

    // Create new card object
    const newCard = {
      id: Date.now().toString(),
      lastFourDigits: cardForm.cardNumber.replace(/\s/g, "").slice(-4),
      cardType: cardForm.cardNumber.startsWith("4") ? "visa" : "mastercard",
    };

    // Add card and reset form
    dispatch(addCard(newCard));
    dispatch(resetCardForm());
    dispatch(setCardViewMode("list"));
  };

  return (
    <SellerLayout>
      <div className="SellerCards">
        {viewMode === "list" ? (
          <div className="SellerCards__list-view">
            <div className="SellerCards__header">
              <h3 className="SellerCards__subtitle">Saved Cards</h3>
              <button
                className="SellerCards__add-btn"
                onClick={toggleAddCardView}
              >
                <FaPlus /> Add New Card
              </button>
            </div>

            <div className="SellerCards__cards-list">
              {cards.length > 0 ? (
                cards.map((card) => (
                  <div className="SellerCards__card-item" key={card.id}>
                    <div className="SellerCards__card-content">
                      <div className="SellerCards__card-logo">
                        <img
                          src={card.cardType === "visa"
                            ? "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Visa_Inc._logo.svg/200px-Visa_Inc._logo.svg.png"
                            : "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/200px-Mastercard-logo.svg.png"
                          }
                          alt={card.cardType}
                        />
                      </div>
                      <div className="SellerCards__card-number">
                        •••• •••• •••• {card.lastFourDigits}
                      </div>
                    </div>
                    <button
                      className="SellerCards__delete-btn"
                      onClick={() => handleDeleteCard(card.id)}
                      aria-label="Delete card"
                    >
                      <FaTrash />
                    </button>
                  </div>
                ))
              ) : (
                <div className="SellerCards__empty-state">
                  <p>You have no saved payment methods yet.</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="SellerCards__add-view">
            <div className="SellerCards__header">
              <h3 className="SellerCards__subtitle">Add New Card</h3>
            </div>

            <div className="SellerCards__form">
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit();
                }}
              >
                <div className="SellerCards__form-row">
                  <div className="SellerCards__input-field SellerCards__input-field--full">
                    <div className="SellerCards__input-container">
                      <div className="SellerCards__input-icon">
                        <FaCreditCard />
                      </div>
                      <input
                        type="text"
                        id="nameOnCard"
                        name="nameOnCard"
                        value={cardForm.nameOnCard}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Name on card"
                        required
                        className="SellerCards__input"
                      />
                    </div>
                  </div>
                </div>

                <div className="SellerCards__form-row">
                  <div className="SellerCards__input-field SellerCards__input-field--full">
                    <div className="SellerCards__input-container">
                      <div className="SellerCards__input-icon">
                        <FaCreditCard />
                      </div>
                      <input
                        type="text"
                        id="cardNumber"
                        name="cardNumber"
                        value={cardForm.cardNumber}
                        onChange={(e) => handleCardNumberChange(e.target.value)}
                        placeholder="Card number"
                        required
                        maxLength={19}
                        className="SellerCards__input"
                      />
                    </div>
                  </div>
                </div>

                <div className="SellerCards__form-row">
                  <div className="SellerCards__input-field">
                    <div className="SellerCards__input-container">
                      <div className="SellerCards__input-icon">
                        <FaCalendarAlt />
                      </div>
                      <input
                        type="text"
                        id="expiryDate"
                        name="expiryDate"
                        value={cardForm.expiryDate}
                        onChange={(e) => handleExpiryChange(e.target.value)}
                        placeholder="MM/YY"
                        required
                        maxLength={5}
                        className="SellerCards__input"
                      />
                    </div>
                  </div>

                  <div className="SellerCards__input-field">
                    <div className="SellerCards__input-container">
                      <div className="SellerCards__input-icon">
                        <FaLock />
                      </div>
                      <input
                        type="text"
                        id="cvv"
                        name="cvv"
                        value={cardForm.cvv}
                        onChange={(e) =>
                          handleCvvChange(e.target.value.replace(/\D/g, ""))
                        }
                        placeholder="CVV"
                        required
                        maxLength={4}
                        pattern="[0-9]{3,4}"
                        className="SellerCards__input"
                      />
                    </div>
                  </div>
                </div>

                <div className="SellerCards__form-actions">
                  <button type="submit" className="SellerCards__submit-btn">
                    Add Card
                  </button>
                </div>
              </form>
            </div>

            <div className="SellerCards__form-actions">
              <button
                className="SellerCards__cancel-btn"
                onClick={toggleAddCardView}
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </SellerLayout>
  );
};

export default SellerCards;
