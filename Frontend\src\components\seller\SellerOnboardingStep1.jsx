import React, { useState, useEffect } from 'react';
import './SellerOnboardingStep1.css';

const SellerOnboardingStep1 = ({ formData, onInputChange, onExperienceChange, onAddExperience, onNext, fieldErrors }) => {
  const [previewUrl, setPreviewUrl] = useState(formData.profilePic || null);

  // Sync previewUrl with formData.profilePic changes
  useEffect(() => {
    if (formData.profilePic && formData.profilePic !== previewUrl) {
      setPreviewUrl(formData.profilePic);
    }
  }, [formData.profilePic, previewUrl]);

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Create preview URL for immediate display
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target.result);
      };
      reader.readAsDataURL(file);

      // Store the selected file in the parent component for later upload
      onInputChange('selectedImageFile', file);

      console.log('Image selected for later upload:', file.name);
    }
  };



  return (
    <div className="seller-onboarding-step1-container max-container">
      {/* Progress Bar */}
      <div className="progress-bar">
        <div className="step active">1</div>
        <div className="progress-line" />
        <div className="step">2</div>
      </div>

      <div className="form-grid">
        {/* Description Section */}
        <div className="description-section">
          <div className="section-title">Description</div>
          <div className="description-box">
            <textarea
              className={`description-textarea ${fieldErrors?.description ? 'error' : ''}`}
              placeholder="Write Description.."
              rows={3}
              value={formData.description}
              onChange={e => onInputChange('description', e.target.value)}
            />
            {fieldErrors?.description && (
              <div className="field-error">{fieldErrors.description}</div>
            )}
          </div>
        </div>

        {/* Profile Pic & Experience Section */}
        <div className="profile-experience-grid">
          {/* Profile Pic */}
          <div className="profile-pic-section">
            <div className="section-title">Profile Pic</div>
            <div className="avatar-upload">
              <div className="avatar-placeholder">
                {previewUrl || formData.profilePic ? (
                  <img
                    src={previewUrl || formData.profilePic}
                    alt="Profile"
                    className="avatar-image"
                  />
                ) : (
                  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="32" cy="32" r="32" fill="var(--light-gray)" />
                    <ellipse cx="32" cy="27" rx="12" ry="12" fill="#fff" />
                    <ellipse cx="32" cy="50" rx="16" ry="10" fill="#fff" />
                  </svg>
                )}
              </div>
              <input
                type="file"
                id="profilePicInput"
                accept="image/*"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />
              <div className="upload-buttons">
                <button
                  type="button"
                  className="btn btn-outline upload-btn"
                  onClick={() => document.getElementById('profilePicInput').click()}
                >
                  Choose Photo
                </button>
                {formData.selectedImageFile && (
                  <div className="selected-file-info">
                    <span className="file-name">{formData.selectedImageFile.name}</span>
                    <span className="file-status">Ready for upload</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Experience */}
          <div className="experience-section">
            <div className="section-title">Experience</div>
            {formData.experiences.map((exp, idx) => (
              <div className="experience-row" key={idx}>
                <input
                  type="text"
                  className={`input ${fieldErrors?.experiences && idx === 0 ? 'error' : ''}`}
                  placeholder="Enter School Name"
                  value={exp.schoolName}
                  onChange={e => onExperienceChange(idx, 'schoolName', e.target.value)}
                />
                <input
                  type="text"
                  className="input"
                  placeholder="Enter Position"
                  value={exp.position}
                  onChange={e => onExperienceChange(idx, 'position', e.target.value)}
                />
                <div className="year-fields">
                  <input
                    type="text"
                    className="input year-input"
                    placeholder="From Year"
                    value={exp.fromYear}
                    onChange={e => onExperienceChange(idx, 'fromYear', e.target.value)}
                  />
                  <input
                    type="text"
                    className="input year-input"
                    placeholder="To Year"
                    value={exp.toYear}
                    onChange={e => onExperienceChange(idx, 'toYear', e.target.value)}
                  />
                </div>
              </div>
            ))}
            {fieldErrors?.experiences && (
              <div className="field-error">{fieldErrors.experiences}</div>
            )}
            <div className="add-more-link" onClick={onAddExperience}>
              + Add More
            </div>
          </div>
        </div>
      </div>



      {/* Next Button */}
      <div className="next-btn-row">
        <button className="btn btn-primary next-btn" onClick={onNext}>Next</button>
      </div>
    </div>
  );
};

export default SellerOnboardingStep1;