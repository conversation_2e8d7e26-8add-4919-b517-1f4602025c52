/* AddContentModal Component Styles */
.AddContentModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500; /* Above navbar (1000) and sidebar (200) */
  display: flex;
  align-items: center;
  justify-content: center;
}

.AddContentModal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.AddContentModal__container {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.AddContentModal__header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  padding: var(--basefont) var(--heading6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  font-size: var(--heading5);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.close-btn {
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--heading5);
  cursor: pointer;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Content */
.AddContentModal__content {
  flex: 1;
  overflow-y: auto;
  padding: var(--heading6);
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Form Sections */
.form-section {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
}

.form-section h3 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: var(--smallfont);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
  margin-bottom: var(--basefont);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.form-group label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.form-input,
.form-textarea,
.form-select {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.form-input.error,
.form-textarea.error {
  border-color: var(--error-color);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.error-message {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 4px;
}

/* File Upload */
.file-upload-area {
  position: relative;
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--heading6);
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.file-upload-area.drag-active {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.file-upload-area.error {
  border-color: var(--error-color);
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--smallfont);
  color: var(--gray);
}

.file-upload-placeholder svg {
  font-size: var(--heading4);
  color: var(--primary-color);
}

.file-upload-placeholder p {
  margin: 0;
  font-size: var(--basefont);
  font-weight: 500;
}

.file-upload-placeholder small {
  font-size: var(--smallfont);
  color: var(--gray);
}

.file-selected {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  color: var(--success-color);
  font-weight: 500;
}

.file-selected svg {
  font-size: var(--heading6);
}

.remove-file {
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--border-radius-small);
  transition: background-color 0.3s ease;
}

.remove-file:hover {
  background-color: rgba(var(--error-color-rgb), 0.1);
}

/* Thumbnail Upload */
.thumbnail-upload {
  position: relative;
  width: 150px;
  height: 100px;
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.thumbnail-upload:hover {
  border-color: var(--primary-color);
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
  color: var(--gray);
  background-color: var(--bg-gray);
}

.thumbnail-placeholder svg {
  font-size: var(--heading6);
  color: var(--primary-color);
}

.thumbnail-placeholder span {
  font-size: var(--smallfont);
  font-weight: 500;
}

.thumbnail-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.thumbnail-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-thumbnail {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  color: var(--white);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--border-radius-small);
  font-size: var(--smallfont);
  transition: background-color 0.3s ease;
}

.remove-thumbnail:hover {
  background-color: var(--error-color);
}

/* Footer */
.AddContentModal__footer {
  background-color: var(--bg-gray);
  padding: var(--basefont) var(--heading6);
  border-top: 1px solid var(--light-gray);
  display: flex;
  justify-content: flex-end;
}

.footer-actions {
  display: flex;
  gap: var(--basefont);
}

/* Buttons */
.btn {
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
}

/* Responsive Design */
@media (max-width: 768px) {
  .AddContentModal__container {
    width: 95%;
    max-height: 95vh;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .footer-actions {
    flex-direction: column;
    width: 100%;
  }

  .thumbnail-upload {
    width: 100%;
    height: 120px;
  }
}
