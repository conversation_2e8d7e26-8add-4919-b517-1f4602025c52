import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import SellerLayout from "../../components/seller/SellerLayout";
import "../../styles/SellerMySportsStrategies.css";
import { IoEyeSharp } from "react-icons/io5";

const initialData = [
  {
    id: 1,
    title: "<PERSON> and Coaching Philosophies...",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: true,
    thumbnail: "video-1.jpg",
  },
  {
    id: 2,
    title: "<PERSON>ari - Early Transition Offensive Concepts",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: true,
    thumbnail: "video-2.jpg",
  },
  {
    id: 3,
    title: "WR Fundamentals\nPoA - <PERSON>",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: false,
    thumbnail: "video-3.jpg",
  },
  {
    id: 4,
    title: "<PERSON> and Coaching Philosophies...",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: true,
    thumbnail: "video-1.jpg",
  },
  {
    id: 5,
    title: "<PERSON> <PERSON>ipari - Early Transition Offensive Concepts",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: false,
    thumbnail: "video-2.jpg",
  },
  {
    id: 6,
    title: "WR Fundamentals\nPoA - Herman Wiggins",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: false,
    thumbnail: "video-3.jpg",
  },
  {
    id: 7,
    title: "Frank Martin - Drills and Coaching Philosophies...",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: true,
    thumbnail: "video-1.jpg",
  },
  {
    id: 8,
    title: "John Calipari - Early Transition Offensive Concepts",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: false,
    thumbnail: "video-2.jpg",
  },
];

const SellerMySportsStrategies = () => {
  const [videos, setVideos] = useState(initialData);
  const navigate = useNavigate();

  const toggleStatus = (id) => {
    setVideos((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, status: !item.status } : item
      )
    );
  };

  const handleViewDetails = (id) => {
    navigate(`/seller/strategy-details/${id}`);
  };

  return (
    <SellerLayout>
      <div className="video-status-container">
        <table className="video-table">
          <thead>
            <tr>
              <th>No.</th>
              <th>Videos/Documents</th>
              <th>Date</th>
              <th>Price</th>
              <th>Status</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {videos.map((item, index) => (
              <tr key={item.id}>
                <td>{index + 1}</td>
                <td>
                  <div className="video-doc">
                    <img src={item.thumbnail} alt="video thumb" />
                    <span>{item.title}</span>
                  </div>
                </td>
                <td>{item.date}</td>
                <td>{item.price}</td>
                <td>
                  <label className="switch">
                    <input
                      type="checkbox"
                      checked={item.status}
                      onChange={() => toggleStatus(item.id)}
                    />
                    <span className="slider round"></span>
                  </label>
                </td>
                <td>
                  <IoEyeSharp
                    className="action-icon"
                    onClick={() => handleViewDetails(item.id)}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </SellerLayout>
  );
};

export default SellerMySportsStrategies;
